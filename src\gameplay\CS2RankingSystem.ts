import { EventEmitter } from 'events';

export interface RankInfo {
    id: string;
    name: string;
    tier: number;
    minElo: number;
    maxElo: number;
    icon: string;
    color: string;
}

export interface PlayerRank {
    playerId: string;
    currentRank: string;
    elo: number;
    wins: number;
    losses: number;
    ties: number;
    winStreak: number;
    lossStreak: number;
    seasonWins: number;
    seasonLosses: number;
    peakRank: string;
    peakElo: number;
    lastMatchDate: Date;
    placementMatches: number;
    isCalibrated: boolean;
}

export interface MatchResult {
    playerId: string;
    won: boolean;
    tied: boolean;
    roundsWon: number;
    roundsLost: number;
    kills: number;
    deaths: number;
    assists: number;
    mvpRounds: number;
    score: number;
    averageEnemyElo: number;
}

export interface EloChange {
    playerId: string;
    oldElo: number;
    newElo: number;
    change: number;
    oldRank: string;
    newRank: string;
    rankChanged: boolean;
    reason: string;
}

export class CS2RankingSystem extends EventEmitter {
    private playerRanks: Map<string, PlayerRank> = new Map();
    private ranks: Map<string, RankInfo> = new Map();
    
    // Configurações do sistema de ranking
    private static readonly PLACEMENT_MATCHES = 10;
    private static readonly BASE_ELO_CHANGE = 25;
    private static readonly MAX_ELO_CHANGE = 50;
    private static readonly MIN_ELO_CHANGE = 5;
    private static readonly PERFORMANCE_MULTIPLIER = 0.3;
    private static readonly STREAK_BONUS = 5;
    private static readonly MAX_STREAK_BONUS = 15;

    constructor() {
        super();
        this.initializeRanks();
        console.log('🏆 Sistema de Ranking CS2 inicializado');
    }

    private initializeRanks(): void {
        const rankData: RankInfo[] = [
            // Silver
            { id: 'silver_1', name: 'Silver I', tier: 1, minElo: 0, maxElo: 199, icon: '🥈', color: '#C0C0C0' },
            { id: 'silver_2', name: 'Silver II', tier: 2, minElo: 200, maxElo: 399, icon: '🥈', color: '#C0C0C0' },
            { id: 'silver_3', name: 'Silver III', tier: 3, minElo: 400, maxElo: 599, icon: '🥈', color: '#C0C0C0' },
            { id: 'silver_4', name: 'Silver IV', tier: 4, minElo: 600, maxElo: 799, icon: '🥈', color: '#C0C0C0' },
            { id: 'silver_elite', name: 'Silver Elite', tier: 5, minElo: 800, maxElo: 999, icon: '🥈', color: '#C0C0C0' },
            { id: 'silver_elite_master', name: 'Silver Elite Master', tier: 6, minElo: 1000, maxElo: 1199, icon: '🥈', color: '#C0C0C0' },

            // Gold Nova
            { id: 'gold_nova_1', name: 'Gold Nova I', tier: 7, minElo: 1200, maxElo: 1399, icon: '🥇', color: '#FFD700' },
            { id: 'gold_nova_2', name: 'Gold Nova II', tier: 8, minElo: 1400, maxElo: 1599, icon: '🥇', color: '#FFD700' },
            { id: 'gold_nova_3', name: 'Gold Nova III', tier: 9, minElo: 1600, maxElo: 1799, icon: '🥇', color: '#FFD700' },
            { id: 'gold_nova_master', name: 'Gold Nova Master', tier: 10, minElo: 1800, maxElo: 1999, icon: '🥇', color: '#FFD700' },

            // Master Guardian
            { id: 'master_guardian_1', name: 'Master Guardian I', tier: 11, minElo: 2000, maxElo: 2199, icon: '🛡️', color: '#4169E1' },
            { id: 'master_guardian_2', name: 'Master Guardian II', tier: 12, minElo: 2200, maxElo: 2399, icon: '🛡️', color: '#4169E1' },
            { id: 'master_guardian_elite', name: 'Master Guardian Elite', tier: 13, minElo: 2400, maxElo: 2599, icon: '🛡️', color: '#4169E1' },
            { id: 'distinguished_master_guardian', name: 'Distinguished Master Guardian', tier: 14, minElo: 2600, maxElo: 2799, icon: '🛡️', color: '#4169E1' },

            // Legendary Eagle
            { id: 'legendary_eagle', name: 'Legendary Eagle', tier: 15, minElo: 2800, maxElo: 2999, icon: '🦅', color: '#8B4513' },
            { id: 'legendary_eagle_master', name: 'Legendary Eagle Master', tier: 16, minElo: 3000, maxElo: 3199, icon: '🦅', color: '#8B4513' },

            // Supreme
            { id: 'supreme_master_first_class', name: 'Supreme Master First Class', tier: 17, minElo: 3200, maxElo: 3399, icon: '👑', color: '#800080' },

            // Global Elite
            { id: 'global_elite', name: 'Global Elite', tier: 18, minElo: 3400, maxElo: 9999, icon: '🌟', color: '#FF0000' }
        ];

        rankData.forEach(rank => this.ranks.set(rank.id, rank));
        console.log(`🏆 ${this.ranks.size} ranks carregados`);
    }

    public createPlayerRank(playerId: string): PlayerRank {
        const playerRank: PlayerRank = {
            playerId,
            currentRank: 'silver_1',
            elo: 100, // ELO inicial baixo para placement matches
            wins: 0,
            losses: 0,
            ties: 0,
            winStreak: 0,
            lossStreak: 0,
            seasonWins: 0,
            seasonLosses: 0,
            peakRank: 'silver_1',
            peakElo: 100,
            lastMatchDate: new Date(),
            placementMatches: 0,
            isCalibrated: false
        };

        this.playerRanks.set(playerId, playerRank);
        console.log(`🏆 Rank criado para ${playerId} - ${this.getRankInfo('silver_1')?.name}`);
        
        return playerRank;
    }

    public processMatchResult(matchResult: MatchResult): EloChange {
        let playerRank = this.playerRanks.get(matchResult.playerId);
        
        if (!playerRank) {
            playerRank = this.createPlayerRank(matchResult.playerId);
        }

        const oldElo = playerRank.elo;
        const oldRank = playerRank.currentRank;

        // Calcula mudança de ELO
        const eloChange = this.calculateEloChange(playerRank, matchResult);
        
        // Aplica mudança
        playerRank.elo = Math.max(0, playerRank.elo + eloChange);
        
        // Atualiza estatísticas
        this.updatePlayerStats(playerRank, matchResult);
        
        // Atualiza rank baseado no novo ELO
        const newRank = this.calculateRankFromElo(playerRank.elo);
        const rankChanged = newRank !== oldRank;
        
        if (rankChanged) {
            playerRank.currentRank = newRank;
            
            // Atualiza peak rank se necessário
            if (playerRank.elo > playerRank.peakElo) {
                playerRank.peakElo = playerRank.elo;
                playerRank.peakRank = newRank;
            }
        }

        playerRank.lastMatchDate = new Date();

        const change: EloChange = {
            playerId: matchResult.playerId,
            oldElo,
            newElo: playerRank.elo,
            change: eloChange,
            oldRank,
            newRank,
            rankChanged,
            reason: this.getChangeReason(matchResult, eloChange)
        };

        console.log(`📊 ${matchResult.playerId}: ${oldElo} → ${playerRank.elo} (${eloChange > 0 ? '+' : ''}${eloChange}) ${rankChanged ? `[${oldRank} → ${newRank}]` : ''}`);
        
        this.emit('eloChanged', change);
        
        if (rankChanged) {
            this.emit('rankChanged', {
                playerId: matchResult.playerId,
                oldRank,
                newRank,
                newElo: playerRank.elo
            });
        }

        return change;
    }

    private calculateEloChange(playerRank: PlayerRank, matchResult: MatchResult): number {
        let baseChange = CS2RankingSystem.BASE_ELO_CHANGE;

        // Ajuste para placement matches
        if (!playerRank.isCalibrated) {
            baseChange *= 2; // Mudanças maiores durante calibração
        }

        // Fator de vitória/derrota
        let winFactor = 0;
        if (matchResult.won) {
            winFactor = 1;
        } else if (matchResult.tied) {
            winFactor = 0.1;
        } else {
            winFactor = -1;
        }

        // Ajuste baseado na diferença de ELO dos oponentes
        const eloExpectation = this.calculateExpectedResult(playerRank.elo, matchResult.averageEnemyElo);
        const surpriseFactor = winFactor - eloExpectation;

        // Performance individual
        const performanceMultiplier = this.calculatePerformanceMultiplier(matchResult);

        // Streak bonus/penalty
        const streakBonus = this.calculateStreakBonus(playerRank, matchResult.won);

        // Calcula mudança final
        let eloChange = baseChange * winFactor * (1 + surpriseFactor) * performanceMultiplier + streakBonus;

        // Limita mudança
        eloChange = Math.max(-CS2RankingSystem.MAX_ELO_CHANGE, Math.min(CS2RankingSystem.MAX_ELO_CHANGE, eloChange));
        eloChange = Math.abs(eloChange) < CS2RankingSystem.MIN_ELO_CHANGE ? 
            (eloChange > 0 ? CS2RankingSystem.MIN_ELO_CHANGE : -CS2RankingSystem.MIN_ELO_CHANGE) : eloChange;

        return Math.round(eloChange);
    }

    private calculateExpectedResult(playerElo: number, enemyElo: number): number {
        // Fórmula ELO padrão
        return 1 / (1 + Math.pow(10, (enemyElo - playerElo) / 400));
    }

    private calculatePerformanceMultiplier(matchResult: MatchResult): number {
        // Calcula performance baseada em KDA e score
        const kda = matchResult.deaths > 0 ? 
            (matchResult.kills + matchResult.assists) / matchResult.deaths : 
            matchResult.kills + matchResult.assists;

        const kdaMultiplier = Math.min(1.5, Math.max(0.5, kda / 1.0));
        const mvpMultiplier = 1 + (matchResult.mvpRounds * 0.1);
        
        return 1 + ((kdaMultiplier + mvpMultiplier - 2) * CS2RankingSystem.PERFORMANCE_MULTIPLIER);
    }

    private calculateStreakBonus(playerRank: PlayerRank, won: boolean): number {
        if (won && playerRank.winStreak >= 3) {
            return Math.min(CS2RankingSystem.MAX_STREAK_BONUS, 
                playerRank.winStreak * CS2RankingSystem.STREAK_BONUS);
        } else if (!won && playerRank.lossStreak >= 3) {
            return -Math.min(CS2RankingSystem.MAX_STREAK_BONUS, 
                playerRank.lossStreak * CS2RankingSystem.STREAK_BONUS);
        }
        return 0;
    }

    private updatePlayerStats(playerRank: PlayerRank, matchResult: MatchResult): void {
        if (matchResult.won) {
            playerRank.wins++;
            playerRank.seasonWins++;
            playerRank.winStreak++;
            playerRank.lossStreak = 0;
        } else if (matchResult.tied) {
            playerRank.ties++;
            playerRank.winStreak = 0;
            playerRank.lossStreak = 0;
        } else {
            playerRank.losses++;
            playerRank.seasonLosses++;
            playerRank.lossStreak++;
            playerRank.winStreak = 0;
        }

        // Verifica se completou placement matches
        if (!playerRank.isCalibrated) {
            playerRank.placementMatches++;
            if (playerRank.placementMatches >= CS2RankingSystem.PLACEMENT_MATCHES) {
                playerRank.isCalibrated = true;
                console.log(`🎯 ${playerRank.playerId} completou placement matches - Rank: ${this.getRankInfo(playerRank.currentRank)?.name}`);
                this.emit('placementCompleted', {
                    playerId: playerRank.playerId,
                    finalRank: playerRank.currentRank,
                    finalElo: playerRank.elo
                });
            }
        }
    }

    private calculateRankFromElo(elo: number): string {
        for (const [rankId, rankInfo] of this.ranks) {
            if (elo >= rankInfo.minElo && elo <= rankInfo.maxElo) {
                return rankId;
            }
        }
        return 'silver_1'; // Fallback
    }

    private getChangeReason(matchResult: MatchResult, eloChange: number): string {
        if (matchResult.won) {
            return eloChange > 30 ? 'Vitória dominante' : 'Vitória';
        } else if (matchResult.tied) {
            return 'Empate';
        } else {
            return eloChange < -30 ? 'Derrota pesada' : 'Derrota';
        }
    }

    public getPlayerRank(playerId: string): PlayerRank | undefined {
        return this.playerRanks.get(playerId);
    }

    public getRankInfo(rankId: string): RankInfo | undefined {
        return this.ranks.get(rankId);
    }

    public getAllRanks(): RankInfo[] {
        return Array.from(this.ranks.values()).sort((a, b) => a.tier - b.tier);
    }

    public getLeaderboard(limit: number = 100): Array<{rank: number, playerRank: PlayerRank, rankInfo: RankInfo}> {
        const sortedPlayers = Array.from(this.playerRanks.values())
            .filter(p => p.isCalibrated)
            .sort((a, b) => b.elo - a.elo)
            .slice(0, limit);

        return sortedPlayers.map((playerRank, index) => ({
            rank: index + 1,
            playerRank,
            rankInfo: this.getRankInfo(playerRank.currentRank)!
        }));
    }

    public getRankDistribution(): Map<string, number> {
        const distribution = new Map<string, number>();
        
        // Inicializa com 0 para todos os ranks
        this.ranks.forEach((_, rankId) => {
            distribution.set(rankId, 0);
        });

        // Conta jogadores por rank
        this.playerRanks.forEach((playerRank) => {
            if (playerRank.isCalibrated) {
                const current = distribution.get(playerRank.currentRank) || 0;
                distribution.set(playerRank.currentRank, current + 1);
            }
        });

        return distribution;
    }

    public simulateDecay(): void {
        // Simula decay de rank por inatividade (seria executado periodicamente)
        const now = new Date();
        const decayThreshold = 30 * 24 * 60 * 60 * 1000; // 30 dias

        this.playerRanks.forEach((playerRank) => {
            const timeSinceLastMatch = now.getTime() - playerRank.lastMatchDate.getTime();
            
            if (timeSinceLastMatch > decayThreshold && playerRank.isCalibrated) {
                const decayAmount = Math.floor(timeSinceLastMatch / decayThreshold) * 10;
                const oldElo = playerRank.elo;
                const oldRank = playerRank.currentRank;
                
                playerRank.elo = Math.max(0, playerRank.elo - decayAmount);
                const newRank = this.calculateRankFromElo(playerRank.elo);
                
                if (newRank !== oldRank) {
                    playerRank.currentRank = newRank;
                    console.log(`📉 ${playerRank.playerId} sofreu decay: ${oldRank} → ${newRank} (${oldElo} → ${playerRank.elo})`);
                    
                    this.emit('rankDecay', {
                        playerId: playerRank.playerId,
                        oldRank,
                        newRank,
                        eloLost: oldElo - playerRank.elo
                    });
                }
            }
        });
    }

    public resetSeason(): void {
        // Reset de temporada
        this.playerRanks.forEach((playerRank) => {
            playerRank.seasonWins = 0;
            playerRank.seasonLosses = 0;
            playerRank.winStreak = 0;
            playerRank.lossStreak = 0;
            
            // Soft reset do ELO (reduz para próximo do rank anterior)
            if (playerRank.isCalibrated) {
                const currentRankInfo = this.getRankInfo(playerRank.currentRank);
                if (currentRankInfo) {
                    playerRank.elo = Math.max(
                        currentRankInfo.minElo,
                        playerRank.elo * 0.8 // Reduz 20% do ELO
                    );
                    playerRank.currentRank = this.calculateRankFromElo(playerRank.elo);
                }
            }
        });

        console.log('🔄 Temporada resetada - ELO de todos os jogadores ajustado');
        this.emit('seasonReset');
    }
}
