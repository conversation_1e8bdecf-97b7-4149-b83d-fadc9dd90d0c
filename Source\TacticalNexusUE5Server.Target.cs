using UnrealBuildTool;
using System.Collections.Generic;

public class TacticalNexusUE5ServerTarget : TargetRules
{
	public TacticalNexusUE5ServerTarget(TargetInfo Target) : base(Target)
	{
		Type = TargetType.Server;
		DefaultBuildSettings = BuildSettingsVersion.V2;
		ExtraModuleNames.AddRange(new string[] { "TacticalNexusUE5" });
		
		// Server optimizations
		bUseLoggingInShipping = true;
		bUseChecksInShipping = true;
		bCompileWithStatsWithoutEngine = true;
		bCompileWithPluginSupport = true;
		
		// Performance settings for dedicated server
		bUseUnityBuild = true;
		bUsePCHFiles = true;
		bUseSharedPCHs = true;
		
		// Enable modern C++ features
		CppStandard = CppStandardVersion.Cpp17;
		
		// Steam integration for server
		if (Target.Platform == UnrealTargetPlatform.Win64)
		{
			GlobalDefinitions.Add("STEAM_ENABLED=1");
			GlobalDefinitions.Add("DEDICATED_SERVER=1");
		}
	}
}
