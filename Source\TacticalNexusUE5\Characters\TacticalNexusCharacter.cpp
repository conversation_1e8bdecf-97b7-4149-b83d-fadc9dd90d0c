// Copyright Tactical Nexus Team. All Rights Reserved.

#include "TacticalNexusCharacter.h"
#include "Camera/CameraComponent.h"
#include "Components/CapsuleComponent.h"
#include "Components/InputComponent.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "GameFramework/Controller.h"
#include "GameFramework/SpringArmComponent.h"
#include "Components/SkeletalMeshComponent.h"
#include "Components/AudioComponent.h"
#include "Engine/Engine.h"
#include "Net/UnrealNetwork.h"
#include "Kismet/GameplayStatics.h"
#include "Sound/SoundCue.h"
#include "../TacticalNexusUE5.h"

ATacticalNexusCharacter::ATacticalNexusCharacter()
{
    PrimaryActorTick.bCanEverTick = true;
    bReplicates = true;
    bNetUseOwnerRelevancy = true;

    // Set size for collision capsule
    GetCapsuleComponent()->InitCapsuleSize(42.f, 96.0f);

    // Configure character movement
    GetCharacterMovement()->bOrientRotationToMovement = false;
    GetCharacterMovement()->RotationRate = FRotator(0.0f, 540.0f, 0.0f);
    GetCharacterMovement()->JumpZVelocity = 420.f;
    GetCharacterMovement()->AirControl = 0.2f;
    GetCharacterMovement()->MaxWalkSpeed = 600.f;
    GetCharacterMovement()->MinAnalogWalkSpeed = 20.f;
    GetCharacterMovement()->BrakingDecelerationWalking = 2000.f;

    // Don't rotate when the controller rotates
    bUseControllerRotationPitch = false;
    bUseControllerRotationYaw = true;
    bUseControllerRotationRoll = false;

    // Create first person camera component
    FirstPersonCamera = CreateDefaultSubobject<UCameraComponent>(TEXT("FirstPersonCamera"));
    FirstPersonCamera->SetupAttachment(GetCapsuleComponent());
    FirstPersonCamera->SetRelativeLocation(FVector(-39.56f, 1.75f, 64.f));
    FirstPersonCamera->bUsePawnControlRotation = true;

    // Create first person mesh component
    FirstPersonMesh = CreateDefaultSubobject<USkeletalMeshComponent>(TEXT("FirstPersonMesh"));
    FirstPersonMesh->SetOnlyOwnerSee(true);
    FirstPersonMesh->SetupAttachment(FirstPersonCamera);
    FirstPersonMesh->bCastDynamicShadow = false;
    FirstPersonMesh->CastShadow = false;
    FirstPersonMesh->SetRelativeRotation(FRotator(1.9f, -19.19f, 5.2f));
    FirstPersonMesh->SetRelativeLocation(FVector(-0.5f, -4.4f, -155.7f));

    // Create footstep audio component
    FootstepAudioComponent = CreateDefaultSubobject<UAudioComponent>(TEXT("FootstepAudio"));
    FootstepAudioComponent->SetupAttachment(RootComponent);
    FootstepAudioComponent->bAutoActivate = false;

    // Initialize player state
    PlayerTeam = EPlayerTeam::None;
    CurrentPlayerState = EPlayerState::Connecting;
    Health = 100.0f;
    MaxHealth = 100.0f;
    Armor = 0.0f;
    MaxArmor = 100.0f;

    // Initialize movement settings
    WalkSpeed = 250.0f;
    RunSpeed = 600.0f;
    CrouchSpeed = 150.0f;
    AimingSpeedMultiplier = 0.5f;

    // Initialize camera settings
    BaseTurnRate = 45.0f;
    BaseLookUpRate = 45.0f;
    DefaultFOV = 90.0f;
    AimingFOV = 65.0f;
    CurrentFOV = DefaultFOV;
    TargetFOV = DefaultFOV;

    // Initialize weapon state
    CurrentWeapon = nullptr;
    CurrentWeaponIndex = -1;

    // Initialize movement state
    bIsWalking = false;
    bIsRunning = false;
    bIsAiming = false;
    bWantsToWalk = false;
    bWantsToRun = false;

    // Initialize footstep timing
    LastFootstepTime = 0.0f;
    FootstepInterval = 0.5f;
}

void ATacticalNexusCharacter::BeginPlay()
{
    Super::BeginPlay();
    
    if (FirstPersonCamera)
    {
        CurrentFOV = FirstPersonCamera->FieldOfView;
        TargetFOV = CurrentFOV;
    }

    UE_LOG(LogTacticalNexus, Log, TEXT("TacticalNexusCharacter BeginPlay - Team: %d"), (int32)PlayerTeam);
}

void ATacticalNexusCharacter::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(ATacticalNexusCharacter, PlayerTeam);
    DOREPLIFETIME(ATacticalNexusCharacter, CurrentPlayerState);
    DOREPLIFETIME(ATacticalNexusCharacter, Health);
    DOREPLIFETIME(ATacticalNexusCharacter, MaxHealth);
    DOREPLIFETIME(ATacticalNexusCharacter, Armor);
    DOREPLIFETIME(ATacticalNexusCharacter, MaxArmor);
    DOREPLIFETIME(ATacticalNexusCharacter, PlayerStats);
    DOREPLIFETIME(ATacticalNexusCharacter, PlayerEconomy);
    DOREPLIFETIME(ATacticalNexusCharacter, CurrentWeapon);
    DOREPLIFETIME(ATacticalNexusCharacter, WeaponInventory);
    DOREPLIFETIME(ATacticalNexusCharacter, CurrentWeaponIndex);
}

void ATacticalNexusCharacter::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Update camera FOV interpolation
    UpdateCameraFOV();

    // Update movement speed based on current state
    UpdateMovementSpeed();

    // Handle footstep sounds
    if (GetVelocity().Size() > 10.0f && GetCharacterMovement()->IsMovingOnGround())
    {
        float CurrentTime = GetWorld()->GetTimeSeconds();
        if (CurrentTime - LastFootstepTime >= FootstepInterval)
        {
            PlayFootstepSound();
            LastFootstepTime = CurrentTime;
        }
    }
}

void ATacticalNexusCharacter::SetupPlayerInputComponent(UInputComponent* PlayerInputComponent)
{
    Super::SetupPlayerInputComponent(PlayerInputComponent);

    // Movement bindings
    PlayerInputComponent->BindAxis("MoveForward", this, &ATacticalNexusCharacter::MoveForward);
    PlayerInputComponent->BindAxis("MoveRight", this, &ATacticalNexusCharacter::MoveRight);

    // Mouse look bindings
    PlayerInputComponent->BindAxis("Turn", this, &ATacticalNexusCharacter::Turn);
    PlayerInputComponent->BindAxis("LookUp", this, &ATacticalNexusCharacter::LookUp);

    // Action bindings
    PlayerInputComponent->BindAction("Jump", IE_Pressed, this, &ATacticalNexusCharacter::StartJump);
    PlayerInputComponent->BindAction("Jump", IE_Released, this, &ATacticalNexusCharacter::StopJump);
    
    PlayerInputComponent->BindAction("Crouch", IE_Pressed, this, &ATacticalNexusCharacter::StartCrouch);
    PlayerInputComponent->BindAction("Crouch", IE_Released, this, &ATacticalNexusCharacter::StopCrouch);
    
    PlayerInputComponent->BindAction("Walk", IE_Pressed, this, &ATacticalNexusCharacter::StartWalk);
    PlayerInputComponent->BindAction("Walk", IE_Released, this, &ATacticalNexusCharacter::StopWalk);

    // Weapon bindings
    PlayerInputComponent->BindAction("Fire", IE_Pressed, this, &ATacticalNexusCharacter::StartFire);
    PlayerInputComponent->BindAction("Fire", IE_Released, this, &ATacticalNexusCharacter::StopFire);
    
    PlayerInputComponent->BindAction("ADS", IE_Pressed, this, &ATacticalNexusCharacter::StartAiming);
    PlayerInputComponent->BindAction("ADS", IE_Released, this, &ATacticalNexusCharacter::StopAiming);
    
    PlayerInputComponent->BindAction("Reload", IE_Pressed, this, &ATacticalNexusCharacter::Reload);
    PlayerInputComponent->BindAction("Drop", IE_Pressed, this, &ATacticalNexusCharacter::DropWeapon);
}

void ATacticalNexusCharacter::MoveForward(float Value)
{
    if (Value != 0.0f && Controller != nullptr)
    {
        const FRotator Rotation = Controller->GetControlRotation();
        const FRotator YawRotation(0, Rotation.Yaw, 0);
        const FVector Direction = FRotationMatrix(YawRotation).GetUnitAxis(EAxis::X);
        AddMovementInput(Direction, Value);
    }
}

void ATacticalNexusCharacter::MoveRight(float Value)
{
    if (Value != 0.0f && Controller != nullptr)
    {
        const FRotator Rotation = Controller->GetControlRotation();
        const FRotator YawRotation(0, Rotation.Yaw, 0);
        const FVector Direction = FRotationMatrix(YawRotation).GetUnitAxis(EAxis::Y);
        AddMovementInput(Direction, Value);
    }
}

void ATacticalNexusCharacter::Turn(float Value)
{
    if (Value != 0.0f)
    {
        AddControllerYawInput(Value);
    }
}

void ATacticalNexusCharacter::LookUp(float Value)
{
    if (Value != 0.0f)
    {
        AddControllerPitchInput(Value);
    }
}

void ATacticalNexusCharacter::StartJump()
{
    bPressedJump = true;
    if (JumpSound && GetCharacterMovement()->IsMovingOnGround())
    {
        UGameplayStatics::PlaySoundAtLocation(this, JumpSound, GetActorLocation());
    }
}

void ATacticalNexusCharacter::StopJump()
{
    bPressedJump = false;
}

void ATacticalNexusCharacter::StartCrouch()
{
    Crouch();
}

void ATacticalNexusCharacter::StopCrouch()
{
    UnCrouch();
}

void ATacticalNexusCharacter::StartWalk()
{
    bWantsToWalk = true;
    bIsWalking = true;
    bIsRunning = false;
}

void ATacticalNexusCharacter::StopWalk()
{
    bWantsToWalk = false;
    bIsWalking = false;
    bIsRunning = true;
}

void ATacticalNexusCharacter::StartFire()
{
    if (CurrentWeapon && CurrentPlayerState == EPlayerState::Alive)
    {
        CurrentWeapon->StartFire();
    }
}

void ATacticalNexusCharacter::StopFire()
{
    if (CurrentWeapon)
    {
        CurrentWeapon->StopFire();
    }
}

void ATacticalNexusCharacter::StartAiming()
{
    bIsAiming = true;
    TargetFOV = AimingFOV;
}

void ATacticalNexusCharacter::StopAiming()
{
    bIsAiming = false;
    TargetFOV = DefaultFOV;
}

void ATacticalNexusCharacter::Reload()
{
    if (CurrentWeapon && CurrentPlayerState == EPlayerState::Alive)
    {
        CurrentWeapon->Reload();
    }
}

void ATacticalNexusCharacter::UpdateMovementSpeed()
{
    if (!GetCharacterMovement()) return;

    float TargetSpeed = RunSpeed;
    
    if (bIsCrouched)
    {
        TargetSpeed = CrouchSpeed;
    }
    else if (bIsWalking)
    {
        TargetSpeed = WalkSpeed;
    }
    else if (bIsRunning)
    {
        TargetSpeed = RunSpeed;
    }

    if (bIsAiming)
    {
        TargetSpeed *= AimingSpeedMultiplier;
    }

    GetCharacterMovement()->MaxWalkSpeed = TargetSpeed;
}

void ATacticalNexusCharacter::UpdateCameraFOV()
{
    if (!FirstPersonCamera) return;

    CurrentFOV = FMath::FInterpTo(CurrentFOV, TargetFOV, GetWorld()->GetDeltaSeconds(), 10.0f);
    FirstPersonCamera->SetFieldOfView(CurrentFOV);
}

void ATacticalNexusCharacter::PlayFootstepSound()
{
    if (FootstepSound && FootstepAudioComponent)
    {
        FootstepAudioComponent->SetSound(FootstepSound);
        FootstepAudioComponent->Play();
    }
}

void ATacticalNexusCharacter::EquipWeapon(ABaseWeapon* Weapon)
{
    if (HasAuthority())
    {
        if (CurrentWeapon)
        {
            UnequipCurrentWeapon();
        }

        CurrentWeapon = Weapon;
        if (CurrentWeapon)
        {
            CurrentWeapon->SetOwner(this);
            CurrentWeapon->AttachToComponent(FirstPersonMesh, 
                FAttachmentTransformRules::SnapToTargetNotIncludingScale, 
                TEXT("WeaponSocket"));
        }
    }
    else
    {
        ServerEquipWeapon(Weapon);
    }
}

void ATacticalNexusCharacter::SetPlayerTeam(EPlayerTeam NewTeam)
{
    if (HasAuthority())
    {
        PlayerTeam = NewTeam;
        OnRep_PlayerTeam();
    }
    else
    {
        ServerSetPlayerTeam(NewTeam);
    }
}

bool ATacticalNexusCharacter::ServerSetPlayerTeam_Validate(EPlayerTeam NewTeam)
{
    return true;
}

void ATacticalNexusCharacter::ServerSetPlayerTeam_Implementation(EPlayerTeam NewTeam)
{
    SetPlayerTeam(NewTeam);
}

bool ATacticalNexusCharacter::ServerEquipWeapon_Validate(ABaseWeapon* Weapon)
{
    return Weapon != nullptr;
}

void ATacticalNexusCharacter::ServerEquipWeapon_Implementation(ABaseWeapon* Weapon)
{
    EquipWeapon(Weapon);
}

void ATacticalNexusCharacter::OnRep_PlayerTeam()
{
    UE_LOG(LogTacticalNexus, Log, TEXT("Player team changed to: %d"), (int32)PlayerTeam);
}

void ATacticalNexusCharacter::OnRep_PlayerState()
{
    UE_LOG(LogTacticalNexus, Log, TEXT("Player state changed to: %d"), (int32)CurrentPlayerState);
}

void ATacticalNexusCharacter::OnRep_Health()
{
    // Update UI health display
}

void ATacticalNexusCharacter::OnRep_Armor()
{
    // Update UI armor display
}

void ATacticalNexusCharacter::OnRep_CurrentWeapon()
{
    // Update weapon display
}
