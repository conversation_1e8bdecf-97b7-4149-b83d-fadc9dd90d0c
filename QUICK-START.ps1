# TACTICAL NEXUS - QUICK START
Write-Host "🚀 TACTICAL NEXUS - QUICK START" -ForegroundColor Yellow

# Verificar UE5
$UE5Path = "C:\Program Files\Epic Games\UE_5.4\Engine\Binaries\Win64\UnrealEditor.exe"
if (Test-Path $UE5Path) {
    Write-Host "✅ Unreal Engine 5.4 encontrado" -ForegroundColor Green
} else {
    Write-Host "❌ UE5 não encontrado. Instale primeiro." -ForegroundColor Red
    exit 1
}

# Verificar projeto
if (Test-Path "TacticalNexusUE5.uproject") {
    Write-Host "✅ Projeto encontrado" -ForegroundColor Green
} else {
    Write-Host "❌ Projeto não encontrado" -ForegroundColor Red
    exit 1
}

Write-Host "🎮 Abrindo Unreal Editor..." -ForegroundColor Cyan
Start-Process -FilePath $UE5Path -ArgumentList "$PWD\TacticalNexusUE5.uproject"
Write-Host "✅ Editor iniciado!" -ForegroundColor Green
Write-Host "💡 Próximo: Abra Content/Maps/ProceduralMapGenerator" -ForegroundColor Yellow
