import { CS2GameManager } from '../gameplay/CS2GameManager';
import { CS2BuyMenu } from '../ui/CS2BuyMenu';

export class CS2IntegrationTest {
    private gameManager: CS2GameManager;
    private buyMenu?: CS2BuyMenu;
    private testPlayers: string[] = [];

    constructor() {
        this.gameManager = new CS2GameManager({
            tickRate: 128,
            maxPlayers: 10,
            friendlyFire: false,
            autoBalance: true
        });

        this.setupEventListeners();
        console.log('🧪 CS2 Integration Test inicializado');
    }

    private setupEventListeners(): void {
        this.gameManager.on('matchStarted', (match) => {
            console.log(`🎮 Match iniciado: ${match.id}`);
            this.initializeBuyMenu();
        });

        this.gameManager.on('roundStarted', (data) => {
            console.log(`🎯 Round ${data.roundNumber} iniciado - Freeze Time: ${data.freezeTime}s`);
            this.simulatePlayerActions();
        });

        this.gameManager.on('roundEnded', (data) => {
            console.log(`🏁 Round ${data.roundNumber} finalizado - Vencedor: ${data.winner}`);
            this.logPlayerStats();
        });

        this.gameManager.on('matchEnded', (data) => {
            console.log(`🏆 Match finalizado - Vencedor: ${data.winner}`);
            console.log(`MVP: ${data.match.mvp}`);
            this.logFinalResults(data);
        });

        this.gameManager.on('playerRankChanged', (data) => {
            console.log(`📈 ${data.playerId} mudou de rank: ${data.oldRank} → ${data.newRank}`);
        });
    }

    public async runFullMatchTest(): Promise<void> {
        console.log('🚀 Iniciando teste completo de match CS2...');

        // Cria jogadores de teste
        this.testPlayers = this.createTestPlayers(10);
        
        // Inicia match competitivo
        const matchId = await this.gameManager.createMatch('competitive', this.testPlayers, 'de_dust2');
        
        if (!matchId) {
            console.error('❌ Falha ao criar match');
            return;
        }

        console.log(`✅ Match criado com sucesso: ${matchId}`);
        
        // Simula o match (seria controlado pelo sistema de rounds automaticamente)
        this.simulateMatchProgression();
    }

    private createTestPlayers(count: number): string[] {
        const players: string[] = [];
        
        for (let i = 1; i <= count; i++) {
            const playerId = `player_${i}`;
            players.push(playerId);
            
            // Cria rank para cada jogador
            this.gameManager.getRankingSystem().createPlayerRank(playerId);
        }

        console.log(`👥 ${count} jogadores de teste criados`);
        return players;
    }

    private initializeBuyMenu(): void {
        const economySystem = this.gameManager.getGameModes().getEconomySystem();
        if (!economySystem) {
            console.log('❌ Sistema de economia não disponível');
            return;
        }

        this.buyMenu = new CS2BuyMenu(economySystem);
        
        this.buyMenu.on('itemPurchased', (data) => {
            console.log(`🛒 ${data.playerId} comprou ${data.weapon?.name} por $${data.weapon?.price}`);
        });

        this.buyMenu.on('purchaseFailed', (data) => {
            console.log(`❌ Compra falhou para ${data.playerId}: ${data.reason}`);
        });

        console.log('🛒 Buy Menu inicializado');
    }

    private simulatePlayerActions(): void {
        // Simula ações dos jogadores durante o freeze time
        setTimeout(() => {
            this.simulateBuyPhase();
        }, 1000);

        // Simula ações durante o round
        setTimeout(() => {
            this.simulateRoundActions();
        }, 16000); // Após freeze time
    }

    private simulateBuyPhase(): void {
        console.log('🛒 Simulando fase de compras...');
        
        const economySystem = this.gameManager.getGameModes().getEconomySystem();
        if (!economySystem) return;

        this.testPlayers.forEach((playerId, index) => {
            const playerEconomy = economySystem.getPlayerEconomy(playerId);
            if (!playerEconomy) return;

            // Simula diferentes estratégias de compra
            if (index % 3 === 0) {
                // Full buy
                economySystem.buyItem(playerId, playerEconomy.team === 'terrorist' ? 'ak47' : 'm4a4');
                economySystem.buyItem(playerId, 'kevlar_helmet');
                economySystem.buyItem(playerId, 'he_grenade');
                economySystem.buyItem(playerId, 'flashbang');
            } else if (index % 3 === 1) {
                // Force buy
                economySystem.buyItem(playerId, playerEconomy.team === 'terrorist' ? 'ak47' : 'm4a4');
                economySystem.buyItem(playerId, 'kevlar');
            } else {
                // Eco round
                economySystem.buyItem(playerId, 'p250');
                economySystem.buyItem(playerId, 'kevlar');
            }

            // CT específico
            if (playerEconomy.team === 'counter_terrorist' && index === 1) {
                economySystem.buyItem(playerId, 'defuse_kit');
            }
        });
    }

    private simulateRoundActions(): void {
        console.log('⚔️ Simulando ações do round...');

        // Simula alguns kills
        setTimeout(() => {
            this.simulateKill('player_1', 'player_6', 'ak47');
        }, 2000);

        setTimeout(() => {
            this.simulateKill('player_3', 'player_8', 'm4a4');
        }, 4000);

        setTimeout(() => {
            this.simulateKill('player_2', 'player_7', 'awp');
        }, 6000);

        // Simula plant da bomba
        setTimeout(() => {
            this.simulateBombPlant('player_4', 'A');
        }, 8000);

        // Simula defuse ou explosão
        setTimeout(() => {
            if (Math.random() > 0.5) {
                this.simulateBombDefuse('player_9');
            }
        }, 12000);
    }

    private simulateKill(killerId: string, victimId: string, weapon: string): void {
        console.log(`💀 ${killerId} eliminou ${victimId} com ${weapon}`);
        
        // Adiciona reward econômico
        const economySystem = this.gameManager.getGameModes().getEconomySystem();
        if (economySystem) {
            economySystem.addKillReward(killerId, weapon, victimId);
        }

        // Atualiza estatísticas
        this.gameManager.addDamage(killerId, 100);
        this.gameManager.eliminatePlayer(victimId, killerId);

        // Chance de headshot
        if (Math.random() > 0.7) {
            this.gameManager.addHeadshot(killerId);
            console.log(`🎯 Headshot por ${killerId}!`);
        }
    }

    private simulateBombPlant(playerId: string, site: 'A' | 'B'): void {
        console.log(`💣 ${playerId} plantou a bomba no site ${site}`);
        
        const success = this.gameManager.plantBomb(playerId, site, { x: 100, y: 0, z: 200 });
        if (success) {
            console.log('✅ Bomba plantada com sucesso');
        }
    }

    private simulateBombDefuse(playerId: string): void {
        console.log(`🛡️ ${playerId} tentando desarmar a bomba`);
        
        const economySystem = this.gameManager.getGameModes().getEconomySystem();
        const playerEconomy = economySystem?.getPlayerEconomy(playerId);
        const hasKit = playerEconomy?.inventory.defuseKit || false;
        
        const success = this.gameManager.defuseBomb(playerId, hasKit);
        if (success) {
            console.log(`✅ Bomba desarmada por ${playerId} ${hasKit ? '(com kit)' : '(sem kit)'}`);
        }
    }

    private simulateMatchProgression(): void {
        // O match progride automaticamente através do RoundSystem
        // Aqui apenas logamos o progresso
        const checkProgress = () => {
            const match = this.gameManager.getCurrentMatch();
            if (match && match.status === 'live') {
                console.log(`📊 Progresso: Round ${match.rounds}, Score: ${match.score.terrorist}-${match.score.counterTerrorist}`);
                
                if (match.status !== 'finished') {
                    setTimeout(checkProgress, 10000); // Verifica a cada 10 segundos
                }
            }
        };

        setTimeout(checkProgress, 5000);
    }

    private logPlayerStats(): void {
        const stats = this.gameManager.getAllPlayerStats();
        
        console.log('\n📊 Estatísticas do Round:');
        stats.forEach(stat => {
            console.log(`${stat.playerId} (${stat.team}): ${stat.kills}K/${stat.deaths}D/${stat.assists}A - Rating: ${stat.rating.toFixed(2)}`);
        });
    }

    private logFinalResults(data: any): void {
        console.log('\n🏆 Resultados Finais:');
        console.log(`Vencedor: ${data.winner}`);
        console.log(`MVP: ${data.match.mvp}`);
        console.log(`Duração: ${data.match.rounds} rounds`);
        
        console.log('\n📈 Top Performers:');
        const sortedStats = data.finalStats.sort((a: any, b: any) => b.rating - a.rating);
        sortedStats.slice(0, 5).forEach((stat: any, index: number) => {
            console.log(`${index + 1}. ${stat.playerId}: ${stat.kills}K/${stat.deaths}D/${stat.assists}A - Rating: ${stat.rating.toFixed(2)}`);
        });

        // Mostra mudanças de rank
        console.log('\n🏆 Sistema de Ranking:');
        const leaderboard = this.gameManager.getRankingSystem().getLeaderboard(10);
        leaderboard.forEach((entry, index) => {
            console.log(`${index + 1}. ${entry.playerRank.playerId} - ${entry.rankInfo.name} (${entry.playerRank.elo} ELO)`);
        });
    }

    public testBuyMenu(): void {
        if (!this.buyMenu) {
            console.log('❌ Buy Menu não inicializado');
            return;
        }

        console.log('🧪 Testando Buy Menu...');
        
        // Simula abertura do menu para diferentes jogadores
        this.testPlayers.slice(0, 3).forEach((playerId, index) => {
            setTimeout(() => {
                console.log(`🛒 Abrindo buy menu para ${playerId}`);
                this.buyMenu!.openBuyMenu(playerId);
                
                // Fecha após 3 segundos
                setTimeout(() => {
                    this.buyMenu!.closeBuyMenu();
                }, 3000);
            }, index * 4000);
        });
    }

    public testRankingSystem(): void {
        console.log('🧪 Testando Sistema de Ranking...');
        
        const rankingSystem = this.gameManager.getRankingSystem();
        
        // Simula resultados de matches para testar ranking
        this.testPlayers.forEach((playerId, index) => {
            const won = index % 2 === 0;
            const matchResult = {
                playerId,
                won,
                tied: false,
                roundsWon: won ? 16 : 8,
                roundsLost: won ? 8 : 16,
                kills: 15 + Math.floor(Math.random() * 10),
                deaths: 10 + Math.floor(Math.random() * 8),
                assists: 3 + Math.floor(Math.random() * 5),
                mvpRounds: Math.floor(Math.random() * 3),
                score: 2000 + Math.floor(Math.random() * 1000),
                averageEnemyElo: 1500 + Math.floor(Math.random() * 500)
            };

            rankingSystem.processMatchResult(matchResult);
        });

        // Mostra resultados
        const leaderboard = rankingSystem.getLeaderboard(10);
        console.log('\n🏆 Leaderboard após teste:');
        leaderboard.forEach((entry, index) => {
            console.log(`${index + 1}. ${entry.playerRank.playerId} - ${entry.rankInfo.name} (${entry.playerRank.elo} ELO)`);
        });
    }

    public async runQuickTest(): Promise<void> {
        console.log('⚡ Executando teste rápido...');
        
        // Teste básico de funcionalidades
        this.testPlayers = this.createTestPlayers(6);
        
        const matchId = await this.gameManager.createMatch('casual', this.testPlayers);
        if (matchId) {
            console.log('✅ Match casual criado');
            
            // Testa buy menu
            setTimeout(() => this.testBuyMenu(), 2000);
            
            // Testa ranking
            setTimeout(() => this.testRankingSystem(), 5000);
        }
    }

    // Métodos utilitários para testes manuais
    public getGameManager(): CS2GameManager {
        return this.gameManager;
    }

    public getBuyMenu(): CS2BuyMenu | undefined {
        return this.buyMenu;
    }

    public getTestPlayers(): string[] {
        return [...this.testPlayers];
    }
}

// Função para executar testes
export function runCS2Tests(): void {
    console.log('🧪 Iniciando testes do sistema CS2...');
    
    const test = new CS2IntegrationTest();
    
    // Executa teste rápido primeiro
    test.runQuickTest().then(() => {
        console.log('✅ Teste rápido concluído');
        
        // Executa teste completo após 10 segundos
        setTimeout(() => {
            test.runFullMatchTest().then(() => {
                console.log('✅ Todos os testes concluídos');
            });
        }, 10000);
    });
}

// Exporta instância global para testes manuais
(window as any).CS2Test = new CS2IntegrationTest();
