# Tactical Nexus UE5 Project Setup Script - Simplified Version
# Copyright Tactical Nexus Team. All Rights Reserved.

param(
    [string]$ProjectPath = "C:\TacticalNexusUE5",
    [string]$UE5Path = "C:\Program Files\Epic Games\UE_5.4",
    [switch]$CreateDesktopShortcut,
    [switch]$Verbose
)

Write-Host "🎮 TACTICAL NEXUS UE5 PROJECT SETUP" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

# Create project directory
if (-not (Test-Path $ProjectPath)) {
    Write-Host "📁 Criando diretório do projeto: $ProjectPath" -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $ProjectPath -Force | Out-Null
}

# Create project structure
$ProjectName = "TacticalNexusUE5"
$ProjectFile = "$ProjectPath\$ProjectName.uproject"

Write-Host "🏗️ Criando estrutura do projeto..." -ForegroundColor Yellow

# Create .uproject file
$UProjectContent = @'
{
    "FileVersion": 3,
    "EngineAssociation": "5.4",
    "Category": "",
    "Description": "Tactical Nexus - Counter-Strike 2 Clone in Unreal Engine 5",
    "Modules": [
        {
            "Name": "TacticalNexusUE5",
            "Type": "Runtime",
            "LoadingPhase": "Default",
            "AdditionalDependencies": [
                "Engine",
                "CoreUObject",
                "OnlineSubsystemSteam",
                "Niagara",
                "AudioMixer"
            ]
        }
    ],
    "Plugins": [
        {
            "Name": "OnlineSubsystemSteam",
            "Enabled": true
        },
        {
            "Name": "Niagara",
            "Enabled": true
        },
        {
            "Name": "ReplicationGraph",
            "Enabled": true
        }
    ],
    "TargetPlatforms": [
        "Windows"
    ]
}
'@

$UProjectContent | Out-File -FilePath $ProjectFile -Encoding UTF8
Write-Host "✅ Arquivo .uproject criado" -ForegroundColor Green

# Create directory structure
$Directories = @(
    "Content\Core\GameModes",
    "Content\Weapons\Rifles",
    "Content\Maps\Dust2",
    "Content\UI\MainMenu",
    "Content\Audio\Weapons",
    "Content\VFX\MuzzleFlash",
    "Content\Materials\Weapons",
    "Source\TacticalNexusUE5\Core",
    "Source\TacticalNexusUE5\Weapons",
    "Source\TacticalNexusUE5\Characters",
    "Config",
    "Binaries",
    "Intermediate"
)

foreach ($Dir in $Directories) {
    $FullPath = Join-Path $ProjectPath $Dir
    if (-not (Test-Path $FullPath)) {
        New-Item -ItemType Directory -Path $FullPath -Force | Out-Null
        if ($Verbose) {
            Write-Host "📁 Criado: $Dir" -ForegroundColor Gray
        }
    }
}

Write-Host "✅ Estrutura de diretórios criada" -ForegroundColor Green

# Copy source files if they exist
$CurrentDir = Get-Location
$SourceFiles = @(
    @{Source = "UE5-Source\TacticalNexusUE5\Weapons\BaseWeapon.h"; Dest = "Source\TacticalNexusUE5\Weapons\BaseWeapon.h"},
    @{Source = "UE5-Source\TacticalNexusUE5\Weapons\BaseWeapon.cpp"; Dest = "Source\TacticalNexusUE5\Weapons\BaseWeapon.cpp"},
    @{Source = "UE5-Source\TacticalNexusUE5\Core\TacticalNexusGameMode.h"; Dest = "Source\TacticalNexusUE5\Core\TacticalNexusGameMode.h"}
)

foreach ($File in $SourceFiles) {
    $SourcePath = Join-Path $CurrentDir $File.Source
    $DestPath = Join-Path $ProjectPath $File.Dest
    
    if (Test-Path $SourcePath) {
        Copy-Item $SourcePath $DestPath -Force
        Write-Host "✅ Copiado: $($File.Dest)" -ForegroundColor Green
    } else {
        Write-Warning "⚠️ Arquivo fonte não encontrado: $($File.Source)"
    }
}

# Create Build.cs file
$ModuleBuildCS = @'
using UnrealBuildTool;

public class TacticalNexusUE5 : ModuleRules
{
    public TacticalNexusUE5(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicDependencyModuleNames.AddRange(new string[] { 
            "Core", 
            "CoreUObject", 
            "Engine", 
            "InputCore",
            "OnlineSubsystem",
            "OnlineSubsystemSteam",
            "Niagara",
            "AudioMixer",
            "UMG",
            "Slate",
            "SlateCore"
        });

        PrivateDependencyModuleNames.AddRange(new string[] { 
            "ReplicationGraph"
        });

        if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            PublicDefinitions.Add("STEAM_ENABLED=1");
        }
        else
        {
            PublicDefinitions.Add("STEAM_ENABLED=0");
        }
    }
}
'@

$ModuleBuildCS | Out-File -FilePath "$ProjectPath\Source\TacticalNexusUE5\TacticalNexusUE5.Build.cs" -Encoding UTF8

# Create Target.cs files
$GameTargetCS = @'
using UnrealBuildTool;
using System.Collections.Generic;

public class TacticalNexusUE5Target : TargetRules
{
    public TacticalNexusUE5Target(TargetInfo Target) : base(Target)
    {
        Type = TargetType.Game;
        DefaultBuildSettings = BuildSettingsVersion.V2;
        ExtraModuleNames.AddRange(new string[] { "TacticalNexusUE5" });
        
        bUseUnityBuild = true;
        bUsePCHFiles = true;
        bUseSharedPCHs = true;
        
        if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            GlobalDefinitions.Add("STEAM_ENABLED=1");
        }
    }
}
'@

$GameTargetCS | Out-File -FilePath "$ProjectPath\Source\TacticalNexusUE5.Target.cs" -Encoding UTF8

Write-Host "✅ Arquivos de build criados" -ForegroundColor Green

# Create desktop shortcut
if ($CreateDesktopShortcut) {
    $DesktopPath = [Environment]::GetFolderPath("Desktop")
    $ShortcutPath = "$DesktopPath\Tactical Nexus UE5.lnk"
    
    try {
        $WshShell = New-Object -comObject WScript.Shell
        $Shortcut = $WshShell.CreateShortcut($ShortcutPath)
        $Shortcut.TargetPath = "$UE5Path\Engine\Binaries\Win64\UnrealEditor.exe"
        $Shortcut.Arguments = "`"$ProjectFile`""
        $Shortcut.WorkingDirectory = $ProjectPath
        $Shortcut.Description = "Tactical Nexus UE5 Project"
        $Shortcut.Save()
        
        Write-Host "✅ Atalho criado na área de trabalho" -ForegroundColor Green
    } catch {
        Write-Warning "⚠️ Erro ao criar atalho: $($_.Exception.Message)"
    }
}

Write-Host ""
Write-Host "🎉 PROJETO UE5 CONFIGURADO COM SUCESSO!" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green
Write-Host ""
Write-Host "📍 Localização do projeto: $ProjectPath" -ForegroundColor Cyan
Write-Host "🎮 Arquivo do projeto: $ProjectFile" -ForegroundColor Cyan
Write-Host ""
Write-Host "🚀 PRÓXIMOS PASSOS:" -ForegroundColor Yellow
Write-Host "1. Instalar Unreal Engine 5.4+ se não estiver instalado" -ForegroundColor White
Write-Host "2. Abrir o projeto no Unreal Editor" -ForegroundColor White
Write-Host "3. Compilar o código C++" -ForegroundColor White
Write-Host "4. Configurar Steam App ID" -ForegroundColor White
Write-Host "5. Criar mapas básicos" -ForegroundColor White
Write-Host ""
Write-Host "💡 Para abrir o projeto (se UE5 estiver instalado):" -ForegroundColor Yellow
Write-Host "   Duplo-clique no arquivo: $ProjectFile" -ForegroundColor Gray
Write-Host ""
Write-Host "🎯 READY TO DEVELOP IN UE5! 🎯" -ForegroundColor Green
