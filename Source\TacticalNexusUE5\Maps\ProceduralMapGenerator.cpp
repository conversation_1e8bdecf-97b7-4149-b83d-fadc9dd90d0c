// Copyright Tactical Nexus Team. All Rights Reserved.
// REVOLUTIONARY PROCEDURAL MAP GENERATION - DUST2 INFINITE

#include "ProceduralMapGenerator.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Components/StaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "ProceduralMeshComponent.h"
#include "Kismet/KismetMathLibrary.h"
#include "NavigationSystem.h"
#include "Net/UnrealNetwork.h"

AProceduralMapGenerator::AProceduralMapGenerator()
{
    PrimaryActorTick.bCanEverTick = true;
    bReplicates = true;
    
    // Initialize components
    TerrainMesh = CreateDefaultSubobject<UProceduralMeshComponent>(TEXT("TerrainMesh"));
    RootComponent = TerrainMesh;
    
    // Initialize default settings
    TerrainResolution = 512;
    MaxBuildingCount = 50;
    MaxVegetationCount = 200;
    bUseAsyncGeneration = true;
    MaxGenerationTime = 16; // 16ms per frame for 60 FPS
    bUseLODSystem = true;
    LODDistance = 5000.0f;
    bUseAdvancedLighting = true;
    
    // Initialize noise settings
    TerrainNoise.Frequency = 0.005f;
    TerrainNoise.Amplitude = 200.0f;
    TerrainNoise.Octaves = 6;
    
    BuildingNoise.Frequency = 0.02f;
    BuildingNoise.Amplitude = 1.0f;
    BuildingNoise.Octaves = 3;
    
    VegetationNoise.Frequency = 0.1f;
    VegetationNoise.Amplitude = 1.0f;
    VegetationNoise.Octaves = 2;
    
    // Initialize generation state
    bIsGenerating = false;
    GenerationProgress = 0.0f;
    CurrentGenerationStep = 0;
    AsyncGenerationTask = nullptr;
}

void AProceduralMapGenerator::BeginPlay()
{
    Super::BeginPlay();
    
    // Initialize random stream with current time if no seed is set
    if (CurrentParameters.Seed == 0)
    {
        CurrentParameters.Seed = FDateTime::Now().GetTicks();
    }
    InitializeRandomStream(CurrentParameters.Seed);
}

void AProceduralMapGenerator::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);
    
    DOREPLIFETIME(AProceduralMapGenerator, CurrentParameters);
}

void AProceduralMapGenerator::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Update async generation if active
    if (bIsGenerating && AsyncGenerationTask)
    {
        // Check if generation is complete
        if (AsyncGenerationTask->IsDone())
        {
            bIsGenerating = false;
            GenerationProgress = 1.0f;
            
            UE_LOG(LogTemp, Warning, TEXT("🗺️ Map generation completed!"));
            
            // Clean up async task
            delete AsyncGenerationTask;
            AsyncGenerationTask = nullptr;
            
            // Finalize generation
            CreateNavigationMesh();
            ValidateMapPlayability();
        }
    }
}

void AProceduralMapGenerator::GenerateMap(const FMapGenerationParameters& Parameters)
{
    CurrentParameters = Parameters;
    InitializeRandomStream(Parameters.Seed);
    
    UE_LOG(LogTemp, Warning, TEXT("🚀 Starting map generation with seed: %d"), Parameters.Seed);
    
    // Clear existing map
    ClearMap();
    
    // Generate based on theme
    switch (Parameters.Theme)
    {
        case EMapTheme::Urban:
            GenerateDust2Procedural(Parameters);
            break;
        case EMapTheme::Industrial:
            GenerateTerrain(Parameters);
            GenerateBuildings(Parameters);
            break;
        default:
            GenerateTerrain(Parameters);
            GenerateBuildings(Parameters);
            GenerateVegetation(Parameters);
            break;
    }
    
    // Generate gameplay elements
    GenerateGameplayElements(Parameters);
    GenerateLighting(Parameters);
    
    UE_LOG(LogTemp, Warning, TEXT("✅ Map generation completed!"));
}

void AProceduralMapGenerator::GenerateDust2Infinite(int32 VariationSeed)
{
    UE_LOG(LogTemp, Warning, TEXT("🌟 Generating Dust2 Infinite Variation #%d"), VariationSeed);
    
    // Set up Dust2 parameters
    FMapGenerationParameters Dust2Params;
    Dust2Params.Theme = EMapTheme::Urban;
    Dust2Params.Size = EMapSize::Small;
    Dust2Params.Complexity = EMapComplexity::Complex;
    Dust2Params.Seed = VariationSeed > 0 ? VariationSeed : FMath::RandRange(1, 999999);
    Dust2Params.VerticalVariation = 0.3f;
    Dust2Params.BuildingDensity = 0.8f;
    Dust2Params.CoverDensity = 0.7f;
    Dust2Params.NumberOfBombSites = 2;
    Dust2Params.NumberOfSpawnPoints = 10;
    Dust2Params.bSymmetrical = false; // Dust2 is not symmetrical
    Dust2Params.bMultiLevel = true;
    Dust2Params.bUndergroundAreas = false;
    Dust2Params.bDestructibleEnvironment = true;
    
    GenerateDust2Procedural(Dust2Params);
}

void AProceduralMapGenerator::GenerateDust2Procedural(const FMapGenerationParameters& Parameters)
{
    UE_LOG(LogTemp, Warning, TEXT("🏗️ Generating Dust2 Procedural Layout..."));
    
    // Create base layout
    CreateDust2Layout();
    
    // Create main areas
    CreateDust2Long();
    CreateDust2Short();
    CreateDust2Mid();
    CreateDust2Sites();
    
    // Place landmarks with variations
    PlaceDust2Landmarks();
    
    // Generate variations
    GenerateDust2Variations();
    
    UE_LOG(LogTemp, Warning, TEXT("✅ Dust2 Procedural generation completed!"));
}

void AProceduralMapGenerator::CreateDust2Layout()
{
    UE_LOG(LogTemp, Warning, TEXT("📐 Creating Dust2 base layout..."));
    
    // Define Dust2 dimensions (scaled for UE5)
    const float MapWidth = 4000.0f;  // 40 meters
    const float MapLength = 6000.0f; // 60 meters
    const float MapHeight = 800.0f;  // 8 meters
    
    // Create terrain base
    TArray<FVector> Vertices;
    TArray<int32> Triangles;
    TArray<FVector> Normals;
    TArray<FVector2D> UVs;
    
    // Generate base terrain mesh
    const int32 GridSize = 64;
    const float StepSize = MapWidth / GridSize;
    
    for (int32 Y = 0; Y <= GridSize; Y++)
    {
        for (int32 X = 0; X <= GridSize; X++)
        {
            float WorldX = X * StepSize - MapWidth * 0.5f;
            float WorldY = Y * StepSize - MapLength * 0.5f;
            float WorldZ = GenerateNoise(WorldX, WorldY, TerrainNoise) * 50.0f; // Subtle height variation
            
            Vertices.Add(FVector(WorldX, WorldY, WorldZ));
            Normals.Add(FVector::UpVector);
            UVs.Add(FVector2D(X / (float)GridSize, Y / (float)GridSize));
        }
    }
    
    // Generate triangles
    for (int32 Y = 0; Y < GridSize; Y++)
    {
        for (int32 X = 0; X < GridSize; X++)
        {
            int32 Index = Y * (GridSize + 1) + X;
            
            // First triangle
            Triangles.Add(Index);
            Triangles.Add(Index + GridSize + 1);
            Triangles.Add(Index + 1);
            
            // Second triangle
            Triangles.Add(Index + 1);
            Triangles.Add(Index + GridSize + 1);
            Triangles.Add(Index + GridSize + 2);
        }
    }
    
    // Create procedural mesh
    TerrainMesh->CreateMeshSection(0, Vertices, Triangles, Normals, UVs, TArray<FColor>(), TArray<FProcMeshTangent>(), true);
    
    UE_LOG(LogTemp, Warning, TEXT("✅ Dust2 base layout created"));
}

void AProceduralMapGenerator::CreateDust2Long()
{
    UE_LOG(LogTemp, Warning, TEXT("🛤️ Creating Dust2 Long area..."));
    
    // Long area coordinates (T spawn to A site)
    FVector LongStart = FVector(-2000.0f, -2500.0f, 0.0f);
    FVector LongEnd = FVector(1500.0f, -2500.0f, 0.0f);
    
    // Create Long corridor with variations
    float CorridorWidth = 400.0f + RandomStream.FRandRange(-100.0f, 100.0f);
    
    // Add to map sections
    FMapSection LongSection;
    LongSection.Location = (LongStart + LongEnd) * 0.5f;
    LongSection.Size = FVector(FVector::Dist(LongStart, LongEnd), CorridorWidth, 300.0f);
    LongSection.SectionType = TEXT("Long");
    LongSection.bIsPlayable = true;
    LongSection.CoverRating = 0.6f;
    LongSection.StrategicValue = 0.8f;
    
    MapSections.Add(LongSection);
    
    UE_LOG(LogTemp, Warning, TEXT("✅ Dust2 Long created with width: %.1f"), CorridorWidth);
}

void AProceduralMapGenerator::CreateDust2Short()
{
    UE_LOG(LogTemp, Warning, TEXT("🏃 Creating Dust2 Short area..."));
    
    // Short area coordinates (CT spawn to A site)
    FVector ShortStart = FVector(2000.0f, 2000.0f, 0.0f);
    FVector ShortEnd = FVector(1500.0f, -1000.0f, 0.0f);
    
    // Create Short path with variations
    float PathWidth = 300.0f + RandomStream.FRandRange(-50.0f, 50.0f);
    
    // Add elevation variation
    float ElevationChange = RandomStream.FRandRange(-100.0f, 100.0f);
    
    FMapSection ShortSection;
    ShortSection.Location = (ShortStart + ShortEnd) * 0.5f;
    ShortSection.Size = FVector(FVector::Dist(ShortStart, ShortEnd), PathWidth, 300.0f);
    ShortSection.SectionType = TEXT("Short");
    ShortSection.bIsPlayable = true;
    ShortSection.CoverRating = 0.4f;
    ShortSection.StrategicValue = 0.7f;
    
    MapSections.Add(ShortSection);
    
    UE_LOG(LogTemp, Warning, TEXT("✅ Dust2 Short created with elevation: %.1f"), ElevationChange);
}

void AProceduralMapGenerator::CreateDust2Mid()
{
    UE_LOG(LogTemp, Warning, TEXT("🎯 Creating Dust2 Mid area..."));
    
    // Mid area (central control area)
    FVector MidCenter = FVector(0.0f, 0.0f, 0.0f);
    float MidRadius = 800.0f + RandomStream.FRandRange(-200.0f, 200.0f);
    
    FMapSection MidSection;
    MidSection.Location = MidCenter;
    MidSection.Size = FVector(MidRadius * 2, MidRadius * 2, 300.0f);
    MidSection.SectionType = TEXT("Mid");
    MidSection.bIsPlayable = true;
    MidSection.CoverRating = 0.8f;
    MidSection.StrategicValue = 0.9f;
    
    MapSections.Add(MidSection);
    
    UE_LOG(LogTemp, Warning, TEXT("✅ Dust2 Mid created with radius: %.1f"), MidRadius);
}

void AProceduralMapGenerator::CreateDust2Sites()
{
    UE_LOG(LogTemp, Warning, TEXT("💣 Creating Dust2 Bomb Sites..."));
    
    // Site A (Long/Short connection)
    FVector SiteALocation = FVector(1500.0f, -1500.0f, 0.0f);
    SiteALocation.X += RandomStream.FRandRange(-200.0f, 200.0f);
    SiteALocation.Y += RandomStream.FRandRange(-200.0f, 200.0f);
    
    // Site B (Tunnels area)
    FVector SiteBLocation = FVector(-1000.0f, 1500.0f, 0.0f);
    SiteBLocation.X += RandomStream.FRandRange(-200.0f, 200.0f);
    SiteBLocation.Y += RandomStream.FRandRange(-200.0f, 200.0f);
    
    BombSiteLocations.Add(SiteALocation);
    BombSiteLocations.Add(SiteBLocation);
    
    // Create site sections
    FMapSection SiteASection;
    SiteASection.Location = SiteALocation;
    SiteASection.Size = FVector(600.0f, 600.0f, 300.0f);
    SiteASection.SectionType = TEXT("BombSite_A");
    SiteASection.bIsPlayable = true;
    SiteASection.CoverRating = 0.7f;
    SiteASection.StrategicValue = 1.0f;
    
    FMapSection SiteBSection;
    SiteBSection.Location = SiteBLocation;
    SiteBSection.Size = FVector(600.0f, 600.0f, 300.0f);
    SiteBSection.SectionType = TEXT("BombSite_B");
    SiteBSection.bIsPlayable = true;
    SiteBSection.CoverRating = 0.7f;
    SiteBSection.StrategicValue = 1.0f;
    
    MapSections.Add(SiteASection);
    MapSections.Add(SiteBSection);
    
    UE_LOG(LogTemp, Warning, TEXT("✅ Dust2 Sites created - A: %s, B: %s"), 
           *SiteALocation.ToString(), *SiteBLocation.ToString());
}

void AProceduralMapGenerator::PlaceDust2Landmarks()
{
    UE_LOG(LogTemp, Warning, TEXT("🏛️ Placing Dust2 landmarks with variations..."));
    
    // Landmark variations based on seed
    int32 LandmarkVariations = RandomStream.RandRange(3, 8);
    
    for (int32 i = 0; i < LandmarkVariations; i++)
    {
        FVector LandmarkLocation = GetRandomLocationInBounds(
            FVector(-2000.0f, -2000.0f, 0.0f),
            FVector(2000.0f, 2000.0f, 0.0f)
        );
        
        // Ensure landmark doesn't interfere with gameplay
        if (IsLocationValid(LandmarkLocation, 300.0f))
        {
            // Create landmark (placeholder for now)
            UE_LOG(LogTemp, Log, TEXT("📍 Landmark placed at: %s"), *LandmarkLocation.ToString());
        }
    }
    
    UE_LOG(LogTemp, Warning, TEXT("✅ %d Dust2 landmarks placed"), LandmarkVariations);
}

void AProceduralMapGenerator::GenerateDust2Variations()
{
    UE_LOG(LogTemp, Warning, TEXT("🎲 Generating Dust2 variations..."));
    
    // Apply random variations based on seed
    float VariationIntensity = RandomStream.FRandRange(0.1f, 0.5f);
    
    // Vary cover positions
    int32 CoverVariations = RandomStream.RandRange(5, 15);
    
    for (int32 i = 0; i < CoverVariations; i++)
    {
        FVector CoverLocation = GetRandomLocationInBounds(
            FVector(-2500.0f, -2500.0f, 0.0f),
            FVector(2500.0f, 2500.0f, 0.0f)
        );
        
        if (IsLocationValid(CoverLocation, 200.0f))
        {
            // Place cover element (crates, walls, etc.)
            UE_LOG(LogTemp, Log, TEXT("📦 Cover placed at: %s"), *CoverLocation.ToString());
        }
    }
    
    UE_LOG(LogTemp, Warning, TEXT("✅ Dust2 variations generated with intensity: %.2f"), VariationIntensity);
}

// Helper function implementations
void AProceduralMapGenerator::InitializeRandomStream(int32 Seed)
{
    RandomStream.Initialize(Seed);
    UE_LOG(LogTemp, Warning, TEXT("🎲 Random stream initialized with seed: %d"), Seed);
}

FVector AProceduralMapGenerator::GetRandomLocationInBounds(const FVector& Min, const FVector& Max)
{
    return FVector(
        RandomStream.FRandRange(Min.X, Max.X),
        RandomStream.FRandRange(Min.Y, Max.Y),
        RandomStream.FRandRange(Min.Z, Max.Z)
    );
}

bool AProceduralMapGenerator::IsLocationValid(const FVector& Location, float MinDistance)
{
    // Check distance from bomb sites
    for (const FVector& BombSite : BombSiteLocations)
    {
        if (FVector::Dist(Location, BombSite) < MinDistance)
        {
            return false;
        }
    }
    
    // Check distance from spawn points
    for (const FVector& SpawnPoint : SpawnPointLocations)
    {
        if (FVector::Dist(Location, SpawnPoint) < MinDistance)
        {
            return false;
        }
    }
    
    return true;
}

float AProceduralMapGenerator::GenerateNoise(float X, float Y, const FAdvancedNoiseSettings& Settings)
{
    float NoiseValue = 0.0f;
    float Amplitude = Settings.Amplitude;
    float Frequency = Settings.Frequency;
    
    for (int32 i = 0; i < Settings.Octaves; i++)
    {
        NoiseValue += FMath::PerlinNoise2D(FVector2D(
            (X + Settings.Offset.X) * Frequency,
            (Y + Settings.Offset.Y) * Frequency
        )) * Amplitude;
        
        Amplitude *= Settings.Persistence;
        Frequency *= Settings.Lacunarity;
    }
    
    return NoiseValue;
}

void AProceduralMapGenerator::ClearMap()
{
    UE_LOG(LogTemp, Warning, TEXT("🧹 Clearing existing map..."));
    
    MapSections.Empty();
    BombSiteLocations.Empty();
    SpawnPointLocations.Empty();
    
    // Clear mesh sections
    TerrainMesh->ClearAllMeshSections();
    
    UE_LOG(LogTemp, Warning, TEXT("✅ Map cleared"));
}
