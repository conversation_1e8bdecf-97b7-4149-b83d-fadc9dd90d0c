// Copyright Tactical Nexus Team. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "BaseWeapon.h"
#include "M4A4Weapon.generated.h"

/**
 * M4A4 Assault Rifle - CS2 Style Implementation
 * 
 * Weapon Stats (CS2 Accurate):
 * - Damage: 33 (body), 132 (head)
 * - Fire Rate: 666 RPM
 * - Magazine Size: 30
 * - Reserve Ammo: 90
 * - Price: $3100
 * - Armor Penetration: 70%
 * - Range: Long
 * - Recoil: Moderate, more controllable than AK-47
 */
UCLASS(Blueprintable, BlueprintType)
class TACTICALNEXUSUE5_API AM4A4Weapon : public ABaseWeapon
{
    GENERATED_BODY()

public:
    AM4A4Weapon();

protected:
    virtual void BeginPlay() override;

public:
    // Override weapon functions for M4A4 specific behavior
    virtual void StartFire() override;
    virtual void Fire() override;
    virtual void ProcessHit(const FHitResult& HitResult) override;

protected:
    // M4A4 specific functions
    virtual void InitializeM4A4Stats();
    virtual void SetupM4A4RecoilPattern();
    virtual FVector CalculateM4A4Spread() override;
    virtual float CalculateM4A4Damage(const FHitResult& HitResult);

    // M4A4 specific properties
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "M4A4 Stats")
    float HeadshotMultiplier;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "M4A4 Stats")
    float ArmorPenetration;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "M4A4 Stats")
    float MovementInaccuracyMultiplier;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "M4A4 Stats")
    float CrouchAccuracyBonus;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "M4A4 Stats")
    float FirstShotAccuracy;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "M4A4 Stats")
    float RecoilControlBonus; // M4A4 is easier to control than AK-47

    // M4A4 recoil pattern (CS2 accurate)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "M4A4 Recoil")
    TArray<FVector2D> M4A4RecoilPattern;

    // Audio specific to M4A4
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "M4A4 Audio")
    USoundCue* M4A4FireSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "M4A4 Audio")
    USoundCue* M4A4ReloadSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "M4A4 Audio")
    USoundCue* M4A4DrawSound;

    // VFX specific to M4A4
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "M4A4 VFX")
    UParticleSystem* M4A4MuzzleFlash;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "M4A4 VFX")
    UParticleSystem* M4A4ShellEject;

private:
    // Internal M4A4 state
    bool bFirstShot;
    float LastFireTime;
    int32 ConsecutiveShots;
};
