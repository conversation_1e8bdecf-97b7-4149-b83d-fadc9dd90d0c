// Copyright Tactical Nexus Team. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "BaseWeapon.h"
#include "AWPWeapon.generated.h"

/**
 * AWP Sniper Rifle - CS2 Style Implementation
 * 
 * Weapon Stats (CS2 Accurate):
 * - Damage: 115 (body), 459 (head)
 * - Fire Rate: 41 RPM
 * - Magazine Size: 10
 * - Reserve Ammo: 30
 * - Price: $4750
 * - Armor Penetration: 97.5%
 * - Range: Very Long
 * - Recoil: Very High but single shot
 * - Scope: 2x zoom levels (2.5x and 10x)
 */
UCLASS(Blueprintable, BlueprintType)
class TACTICALNEXUSUE5_API AAWPWeapon : public ABaseWeapon
{
    GENERATED_BODY()

public:
    AAWPWeapon();

protected:
    virtual void BeginPlay() override;

public:
    // Override weapon functions for AWP specific behavior
    virtual void StartFire() override;
    virtual void StopFire() override;
    virtual void Fire() override;
    virtual void ProcessHit(const FHitResult& HitResult) override;
    virtual bool CanFire() const override;

    // AWP specific functions
    UFUNCTION(BlueprintCallable, Category = "AWP")
    void ToggleScope();

    UFUNCTION(BlueprintCallable, Category = "AWP")
    void ZoomIn();

    UFUNCTION(BlueprintCallable, Category = "AWP")
    void ZoomOut();

    UFUNCTION(BlueprintCallable, Category = "AWP")
    bool IsScoped() const { return bIsScoped; }

    UFUNCTION(BlueprintCallable, Category = "AWP")
    int32 GetZoomLevel() const { return CurrentZoomLevel; }

protected:
    // AWP specific functions
    virtual void InitializeAWPStats();
    virtual FVector CalculateAWPSpread() override;
    virtual float CalculateAWPDamage(const FHitResult& HitResult);
    virtual void HandleScopeZoom();

    // AWP specific properties
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AWP Stats")
    float HeadshotMultiplier;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AWP Stats")
    float ArmorPenetration;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AWP Stats")
    float MovementInaccuracyMultiplier;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AWP Stats")
    float ScopedAccuracyBonus;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AWP Stats")
    float UnscopedInaccuracyPenalty;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AWP Stats")
    float ScopeInTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AWP Stats")
    float ScopeOutTime;

    // Scope settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AWP Scope")
    bool bIsScoped;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AWP Scope")
    int32 CurrentZoomLevel; // 0 = no scope, 1 = 2.5x, 2 = 10x

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AWP Scope")
    float ZoomLevel1FOV; // 2.5x zoom

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AWP Scope")
    float ZoomLevel2FOV; // 10x zoom

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AWP Scope")
    float DefaultFOV;

    // Audio specific to AWP
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AWP Audio")
    USoundCue* AWPFireSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AWP Audio")
    USoundCue* AWPReloadSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AWP Audio")
    USoundCue* AWPScopeInSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AWP Audio")
    USoundCue* AWPScopeOutSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AWP Audio")
    USoundCue* AWPDrawSound;

    // VFX specific to AWP
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AWP VFX")
    UParticleSystem* AWPMuzzleFlash;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AWP VFX")
    UParticleSystem* AWPShellEject;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AWP VFX")
    UParticleSystem* AWPScopeGlint; // Scope reflection for enemies

    // UI Elements
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AWP UI")
    class UMaterialInterface* ScopeOverlayMaterial;

private:
    // Internal AWP state
    bool bCanQuickScope;
    float LastScopeTime;
    float ScopeTransitionTime;
    bool bIsScopeTransitioning;
    
    // Bolt action timing
    float BoltActionTime;
    bool bIsBoltActioning;
    FTimerHandle BoltActionTimerHandle;
    
    // Scope zoom interpolation
    float TargetFOV;
    float CurrentFOV;
};
