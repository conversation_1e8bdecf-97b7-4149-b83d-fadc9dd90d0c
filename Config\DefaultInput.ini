[/Script/Engine.InputSettings]
-AxisConfig=(AxisKeyName="Gamepad_LeftX",AxisProperties=(DeadZone=0.25,Exponent=1.f,Sensitivity=1.f))
-AxisConfig=(AxisKeyName="Gamepad_LeftY",AxisProperties=(DeadZone=0.25,Exponent=1.f,Sensitivity=1.f))
-AxisConfig=(AxisKeyName="Gamepad_RightX",AxisProperties=(DeadZone=0.25,Exponent=1.f,Sensitivity=1.f))
-AxisConfig=(AxisKeyName="Gamepad_RightY",AxisProperties=(DeadZone=0.25,Exponent=1.f,Sensitivity=1.f))
-AxisConfig=(AxisKeyName="MouseX",AxisProperties=(DeadZone=0.f,Exponent=1.f,Sensitivity=0.07f))
-AxisConfig=(AxisKeyName="MouseY",AxisProperties=(DeadZone=0.f,Exponent=1.f,Sensitivity=0.07f))
-AxisConfig=(AxisKeyName="Mouse2D",AxisProperties=(DeadZone=0.f,Exponent=1.f,Sensitivity=0.07f))
+AxisConfig=(AxisKeyName="MouseY",AxisProperties=(DeadZone=0.000000,Sensitivity=0.070000,Exponent=1.000000,bInvert=False))
+AxisConfig=(AxisKeyName="MouseX",AxisProperties=(DeadZone=0.000000,Sensitivity=0.070000,Exponent=1.000000,bInvert=False))
+AxisConfig=(AxisKeyName="Mouse2D",AxisProperties=(DeadZone=0.000000,Sensitivity=0.070000,Exponent=1.000000,bInvert=False))
+AxisConfig=(AxisKeyName="MouseWheelAxis",AxisProperties=(DeadZone=0.000000,Sensitivity=1.000000,Exponent=1.000000,bInvert=False))
+AxisConfig=(AxisKeyName="Gamepad_LeftX",AxisProperties=(DeadZone=0.250000,Sensitivity=1.000000,Exponent=1.000000,bInvert=False))
+AxisConfig=(AxisKeyName="Gamepad_LeftY",AxisProperties=(DeadZone=0.250000,Sensitivity=1.000000,Exponent=1.000000,bInvert=False))
+AxisConfig=(AxisKeyName="Gamepad_RightX",AxisProperties=(DeadZone=0.250000,Sensitivity=1.000000,Exponent=1.000000,bInvert=False))
+AxisConfig=(AxisKeyName="Gamepad_RightY",AxisProperties=(DeadZone=0.250000,Sensitivity=1.000000,Exponent=1.000000,bInvert=False))
bAltEnterTogglesFullscreen=True
bF11TogglesFullscreen=True
bUseMouseForTouch=False
bEnableMouseSmoothing=False
bEnableFOVScaling=True
bCaptureMouseOnLaunch=True
bAlwaysShowTouchInterface=False
bShowConsoleOnFourFingerTap=True
bEnableGestureRecognizer=False
bUseAutocorrect=False
DefaultViewportMouseCaptureMode=CapturePermanently_IncludingInitialMouseDown
DefaultViewportMouseLockMode=LockOnCapture
FOVScale=0.011110
DoubleClickTime=0.200000

; Movement Controls (CS2 Style)
+ActionMappings=(ActionName="Jump",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=SpaceBar)
+ActionMappings=(ActionName="Crouch",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=LeftControl)
+ActionMappings=(ActionName="Walk",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=LeftShift)
+ActionMappings=(ActionName="Use",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=E)

; Weapon Controls
+ActionMappings=(ActionName="Fire",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=LeftMouseButton)
+ActionMappings=(ActionName="ADS",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=RightMouseButton)
+ActionMappings=(ActionName="Reload",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=R)
+ActionMappings=(ActionName="Drop",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=G)
+ActionMappings=(ActionName="Inspect",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=F)

; Weapon Switching (CS2 Style)
+ActionMappings=(ActionName="Weapon1",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=One)
+ActionMappings=(ActionName="Weapon2",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=Two)
+ActionMappings=(ActionName="Weapon3",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=Three)
+ActionMappings=(ActionName="Weapon4",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=Four)
+ActionMappings=(ActionName="Weapon5",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=Five)
+ActionMappings=(ActionName="LastWeapon",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=Q)
+ActionMappings=(ActionName="NextWeapon",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=MouseWheelUp)
+ActionMappings=(ActionName="PrevWeapon",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=MouseWheelDown)

; Grenades (CS2 Style)
+ActionMappings=(ActionName="HEGrenade",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=Six)
+ActionMappings=(ActionName="Flashbang",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=Seven)
+ActionMappings=(ActionName="SmokeGrenade",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=Eight)
+ActionMappings=(ActionName="Decoy",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=Nine)
+ActionMappings=(ActionName="Molotov",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=Zero)

; UI Controls
+ActionMappings=(ActionName="BuyMenu",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=B)
+ActionMappings=(ActionName="Scoreboard",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=Tab)
+ActionMappings=(ActionName="Chat",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=Y)
+ActionMappings=(ActionName="TeamChat",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=U)
+ActionMappings=(ActionName="VoiceChat",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=V)
+ActionMappings=(ActionName="Console",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=Tilde)

; Voting System
+ActionMappings=(ActionName="VoteYes",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=F1)
+ActionMappings=(ActionName="VoteNo",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=F2)
+ActionMappings=(ActionName="CallVote",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=F3)

; Radio Commands (CS2 Style)
+ActionMappings=(ActionName="RadioCommand1",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=Z)
+ActionMappings=(ActionName="RadioCommand2",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=X)
+ActionMappings=(ActionName="RadioCommand3",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=C)

; Spectator Controls
+ActionMappings=(ActionName="SpectateNext",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=MouseWheelUp)
+ActionMappings=(ActionName="SpectatePrev",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=MouseWheelDown)
+ActionMappings=(ActionName="SpectateMode",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=LeftMouseButton)

; Bomb Controls
+ActionMappings=(ActionName="PlantBomb",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=E)
+ActionMappings=(ActionName="DefuseBomb",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=E)

; Movement Axes
+AxisMappings=(AxisName="MoveForward",Scale=1.000000,Key=W)
+AxisMappings=(AxisName="MoveForward",Scale=-1.000000,Key=S)
+AxisMappings=(AxisName="MoveRight",Scale=-1.000000,Key=A)
+AxisMappings=(AxisName="MoveRight",Scale=1.000000,Key=D)

; Mouse Look
+AxisMappings=(AxisName="Turn",Scale=1.000000,Key=MouseX)
+AxisMappings=(AxisName="LookUp",Scale=-1.000000,Key=MouseY)

; Gamepad Support
+AxisMappings=(AxisName="MoveForward",Scale=1.000000,Key=Gamepad_LeftY)
+AxisMappings=(AxisName="MoveRight",Scale=1.000000,Key=Gamepad_LeftX)
+AxisMappings=(AxisName="Turn",Scale=1.000000,Key=Gamepad_RightX)
+AxisMappings=(AxisName="LookUp",Scale=1.000000,Key=Gamepad_RightY)

; Alternative Movement (Arrow Keys)
+AxisMappings=(AxisName="MoveForward",Scale=1.000000,Key=Up)
+AxisMappings=(AxisName="MoveForward",Scale=-1.000000,Key=Down)
+AxisMappings=(AxisName="MoveRight",Scale=-1.000000,Key=Left)
+AxisMappings=(AxisName="MoveRight",Scale=1.000000,Key=Right)

; Console Keys
DefaultPlayerInputClass=/Script/Engine.PlayerInput
DefaultInputComponentClass=/Script/Engine.InputComponent
DefaultTouchInterface=/Engine/MobileResources/HUD/DefaultVirtualJoysticks.DefaultVirtualJoysticks
-ConsoleKeys=Tilde
+ConsoleKeys=Tilde
+ConsoleKeys=Caret

; Quick Buy Binds (CS2 Style)
+ActionMappings=(ActionName="BuyAK47",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=F1)
+ActionMappings=(ActionName="BuyM4A4",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=F2)
+ActionMappings=(ActionName="BuyAWP",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=F3)
+ActionMappings=(ActionName="BuyArmor",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=F4)
+ActionMappings=(ActionName="BuyDefuseKit",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=F5)

; Team Selection
+ActionMappings=(ActionName="JoinTerrorist",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=M)
+ActionMappings=(ActionName="JoinCounterTerrorist",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=N)
+ActionMappings=(ActionName="AutoSelect",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=Comma)
+ActionMappings=(ActionName="Spectate",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=Period)

; Screenshot and Demo
+ActionMappings=(ActionName="Screenshot",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=F12)
+ActionMappings=(ActionName="StartDemo",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=F9)
+ActionMappings=(ActionName="StopDemo",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=F10)
