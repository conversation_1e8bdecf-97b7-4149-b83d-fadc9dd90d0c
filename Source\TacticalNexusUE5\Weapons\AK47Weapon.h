// Copyright Tactical Nexus Team. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "BaseWeapon.h"
#include "AK47Weapon.generated.h"

/**
 * AK-47 Assault Rifle - CS2 Style Implementation
 * 
 * Weapon Stats (CS2 Accurate):
 * - Damage: 36 (body), 144 (head)
 * - Fire Rate: 600 RPM
 * - Magazine Size: 30
 * - Reserve Ammo: 90
 * - Price: $2700
 * - Armor Penetration: 77.5%
 * - Range: Long
 * - Recoil: High but controllable
 */
UCLASS(Blueprintable, BlueprintType)
class TACTICALNEXUSUE5_API AAK47Weapon : public ABaseWeapon
{
    GENERATED_BODY()

public:
    AAK47Weapon();

protected:
    virtual void BeginPlay() override;

public:
    // Override weapon functions for AK-47 specific behavior
    virtual void StartFire() override;
    virtual void Fire() override;
    virtual void ProcessHit(const FHitResult& HitResult) override;

protected:
    // AK-47 specific functions
    virtual void InitializeAK47Stats();
    virtual void SetupAK47RecoilPattern();
    virtual FVector CalculateAK47Spread() override;
    virtual float CalculateAK47Damage(const FHitResult& HitResult);

    // AK-47 specific properties
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AK-47 Stats")
    float HeadshotMultiplier;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AK-47 Stats")
    float ArmorPenetration;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AK-47 Stats")
    float MovementInaccuracyMultiplier;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AK-47 Stats")
    float CrouchAccuracyBonus;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AK-47 Stats")
    float FirstShotAccuracy;

    // AK-47 recoil pattern (CS2 accurate)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AK-47 Recoil")
    TArray<FVector2D> AK47RecoilPattern;

    // Audio specific to AK-47
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AK-47 Audio")
    USoundCue* AK47FireSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AK-47 Audio")
    USoundCue* AK47ReloadSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AK-47 Audio")
    USoundCue* AK47DrawSound;

    // VFX specific to AK-47
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AK-47 VFX")
    UParticleSystem* AK47MuzzleFlash;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AK-47 VFX")
    UParticleSystem* AK47ShellEject;

private:
    // Internal AK-47 state
    bool bFirstShot;
    float LastFireTime;
    int32 ConsecutiveShots;
};
