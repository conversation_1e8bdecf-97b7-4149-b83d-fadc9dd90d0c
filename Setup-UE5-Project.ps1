# Tactical Nexus UE5 Project Setup Script
# Copyright Tactical Nexus Team. All Rights Reserved.

param(
    [string]$ProjectPath = "C:\TacticalNexusUE5",
    [string]$UE5Path = "C:\Program Files\Epic Games\UE_5.4",
    [switch]$CreateDesktopShortcut,
    [switch]$SetupSteamIntegration,
    [switch]$Verbose
)

Write-Host "🎮 TACTICAL NEXUS UE5 PROJECT SETUP" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

# Check if UE5 is installed
if (-not (Test-Path "$UE5Path\Engine\Binaries\Win64\UnrealEditor.exe")) {
    Write-Error "❌ Unreal Engine 5 não encontrado em: $UE5Path"
    Write-Host "📥 Por favor, instale o Unreal Engine 5.4+ através do Epic Games Launcher"
    exit 1
}

Write-Host "✅ Unreal Engine 5 encontrado em: $UE5Path" -ForegroundColor Green

# Create project directory
if (-not (Test-Path $ProjectPath)) {
    Write-Host "📁 Criando diretório do projeto: $ProjectPath" -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $ProjectPath -Force | Out-Null
}

# Create project structure
$ProjectName = "TacticalNexusUE5"
$ProjectFile = "$ProjectPath\$ProjectName.uproject"

Write-Host "🏗️ Criando estrutura do projeto..." -ForegroundColor Yellow

# Create .uproject file
$UProjectContent = @"
{
    "FileVersion": 3,
    "EngineAssociation": "5.4",
    "Category": "",
    "Description": "Tactical Nexus - Counter-Strike 2 Clone in Unreal Engine 5",
    "Modules": [
        {
            "Name": "TacticalNexusUE5",
            "Type": "Runtime",
            "LoadingPhase": "Default",
            "AdditionalDependencies": [
                "Engine",
                "CoreUObject",
                "OnlineSubsystemSteam",
                "Niagara",
                "AudioMixer"
            ]
        },
        {
            "Name": "TacticalNexusUE5Server",
            "Type": "Runtime",
            "LoadingPhase": "Default"
        }
    ],
    "Plugins": [
        {
            "Name": "OnlineSubsystemSteam",
            "Enabled": true
        },
        {
            "Name": "EasyAntiCheat",
            "Enabled": true
        },
        {
            "Name": "Niagara",
            "Enabled": true
        },
        {
            "Name": "SteamAudio",
            "Enabled": true
        },
        {
            "Name": "ReplicationGraph",
            "Enabled": true
        },
        {
            "Name": "NetworkPrediction",
            "Enabled": true
        },
        {
            "Name": "DLSS",
            "Enabled": true
        },
        {
            "Name": "FSR",
            "Enabled": true
        },
        {
            "Name": "Lumen",
            "Enabled": true
        },
        {
            "Name": "Nanite",
            "Enabled": true
        },
        {
            "Name": "ChaosPhysics",
            "Enabled": true
        },
        {
            "Name": "MetaHuman",
            "Enabled": true
        }
    ],
    "TargetPlatforms": [
        "Windows"
    ],
    "EpicSampleNameHash": "0"
}
"@

$UProjectContent | Out-File -FilePath $ProjectFile -Encoding UTF8

Write-Host "✅ Arquivo .uproject criado" -ForegroundColor Green

# Create directory structure
$Directories = @(
    "Content\Core\GameModes",
    "Content\Core\PlayerControllers", 
    "Content\Core\GameStates",
    "Content\Core\PlayerStates",
    "Content\Weapons\Rifles",
    "Content\Weapons\Pistols",
    "Content\Weapons\Grenades",
    "Content\Weapons\Animations",
    "Content\Maps\Dust2",
    "Content\Maps\Mirage",
    "Content\Maps\Inferno",
    "Content\Maps\Cache",
    "Content\Maps\Overpass",
    "Content\UI\MainMenu",
    "Content\UI\HUD",
    "Content\UI\BuyMenu",
    "Content\UI\Scoreboard",
    "Content\Audio\Weapons",
    "Content\Audio\Footsteps",
    "Content\Audio\Music",
    "Content\Audio\Ambient",
    "Content\VFX\MuzzleFlash",
    "Content\VFX\Impacts",
    "Content\VFX\Explosions",
    "Content\VFX\Smoke",
    "Content\Materials\Weapons",
    "Content\Materials\Environment",
    "Content\Materials\Characters",
    "Source\TacticalNexusUE5\Core",
    "Source\TacticalNexusUE5\Weapons",
    "Source\TacticalNexusUE5\Networking",
    "Source\TacticalNexusUE5\Audio",
    "Source\TacticalNexusUE5\Utils",
    "Source\TacticalNexusUE5\Characters",
    "Source\TacticalNexusUE5Server",
    "Config",
    "Binaries",
    "Intermediate",
    "Saved"
)

foreach ($Dir in $Directories) {
    $FullPath = Join-Path $ProjectPath $Dir
    if (-not (Test-Path $FullPath)) {
        New-Item -ItemType Directory -Path $FullPath -Force | Out-Null
        if ($Verbose) {
            Write-Host "📁 Criado: $Dir" -ForegroundColor Gray
        }
    }
}

Write-Host "✅ Estrutura de diretórios criada" -ForegroundColor Green

# Copy source files from current project
$SourceFiles = @(
    @{Source = "UE5-Source\TacticalNexusUE5\Weapons\BaseWeapon.h"; Dest = "Source\TacticalNexusUE5\Weapons\BaseWeapon.h"},
    @{Source = "UE5-Source\TacticalNexusUE5\Weapons\BaseWeapon.cpp"; Dest = "Source\TacticalNexusUE5\Weapons\BaseWeapon.cpp"},
    @{Source = "UE5-Source\TacticalNexusUE5\Core\TacticalNexusGameMode.h"; Dest = "Source\TacticalNexusUE5\Core\TacticalNexusGameMode.h"}
)

foreach ($File in $SourceFiles) {
    $SourcePath = Join-Path (Get-Location) $File.Source
    $DestPath = Join-Path $ProjectPath $File.Dest
    
    if (Test-Path $SourcePath) {
        Copy-Item $SourcePath $DestPath -Force
        Write-Host "✅ Copiado: $($File.Dest)" -ForegroundColor Green
    } else {
        Write-Warning "⚠️ Arquivo fonte não encontrado: $($File.Source)"
    }
}

# Create Build.cs files
$ModuleBuildCS = @"
using UnrealBuildTool;

public class TacticalNexusUE5 : ModuleRules
{
    public TacticalNexusUE5(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicDependencyModuleNames.AddRange(new string[] { 
            "Core", 
            "CoreUObject", 
            "Engine", 
            "InputCore",
            "OnlineSubsystem",
            "OnlineSubsystemSteam",
            "Niagara",
            "AudioMixer",
            "UMG",
            "Slate",
            "SlateCore"
        });

        PrivateDependencyModuleNames.AddRange(new string[] { 
            "Steamworks",
            "EasyAntiCheat",
            "ReplicationGraph",
            "NetworkPrediction"
        });

        // Steam integration
        if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            PublicDefinitions.Add("STEAM_ENABLED=1");
        }
        else
        {
            PublicDefinitions.Add("STEAM_ENABLED=0");
        }
    }
}
"@

$ModuleBuildCS | Out-File -FilePath "$ProjectPath\Source\TacticalNexusUE5\TacticalNexusUE5.Build.cs" -Encoding UTF8

# Create Target.cs files
$GameTargetCS = @'
using UnrealBuildTool;
using System.Collections.Generic;

public class TacticalNexusUE5Target : TargetRules
{
    public TacticalNexusUE5Target(TargetInfo Target) : base(Target)
    {
        Type = TargetType.Game;
        DefaultBuildSettings = BuildSettingsVersion.V2;
        ExtraModuleNames.AddRange(new string[] { "TacticalNexusUE5" });
        
        // Performance optimizations
        bUseUnityBuild = true;
        bUsePCHFiles = true;
        bUseSharedPCHs = true;
        
        // Steam integration
        if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            GlobalDefinitions.Add("STEAM_ENABLED=1");
        }
    }
}
'@

$GameTargetCS | Out-File -FilePath "$ProjectPath\Source\TacticalNexusUE5.Target.cs" -Encoding UTF8

$ServerTargetCS = @'
using UnrealBuildTool;
using System.Collections.Generic;

public class TacticalNexusUE5ServerTarget : TargetRules
{
    public TacticalNexusUE5ServerTarget(TargetInfo Target) : base(Target)
    {
        Type = TargetType.Server;
        DefaultBuildSettings = BuildSettingsVersion.V2;
        ExtraModuleNames.AddRange(new string[] { "TacticalNexusUE5" });
        
        // Server optimizations
        bUseLoggingInShipping = true;
        bUseChecksInShipping = true;
        bCompileWithStatsWithoutEngine = true;
        bCompileWithPluginSupport = true;
    }
}
'@

$ServerTargetCS | Out-File -FilePath "$ProjectPath\Source\TacticalNexusUE5Server.Target.cs" -Encoding UTF8

Write-Host "✅ Arquivos de build criados" -ForegroundColor Green

# Generate project files
Write-Host "🔧 Gerando arquivos do projeto..." -ForegroundColor Yellow
$UBTPath = "$UE5Path\Engine\Binaries\DotNET\UnrealBuildTool.exe"

if (Test-Path $UBTPath) {
    Set-Location $ProjectPath
    & $UBTPath -projectfiles -project="$ProjectFile" -game -rocket -progress
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Arquivos do projeto gerados com sucesso" -ForegroundColor Green
    } else {
        Write-Warning "⚠️ Erro ao gerar arquivos do projeto"
    }
} else {
    Write-Warning "⚠️ UnrealBuildTool não encontrado"
}

# Create desktop shortcut
if ($CreateDesktopShortcut) {
    $DesktopPath = [Environment]::GetFolderPath("Desktop")
    $ShortcutPath = "$DesktopPath\Tactical Nexus UE5.lnk"
    
    $WshShell = New-Object -comObject WScript.Shell
    $Shortcut = $WshShell.CreateShortcut($ShortcutPath)
    $Shortcut.TargetPath = "$UE5Path\Engine\Binaries\Win64\UnrealEditor.exe"
    $Shortcut.Arguments = "`"$ProjectFile`""
    $Shortcut.WorkingDirectory = $ProjectPath
    $Shortcut.IconLocation = "$UE5Path\Engine\Content\Editor\Slate\Icons\Unreal\UnrealEd_64x.ico"
    $Shortcut.Description = "Tactical Nexus UE5 Project"
    $Shortcut.Save()
    
    Write-Host "✅ Atalho criado na área de trabalho" -ForegroundColor Green
}

Write-Host ""
Write-Host "🎉 PROJETO UE5 CONFIGURADO COM SUCESSO!" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green
Write-Host ""
Write-Host "📍 Localização do projeto: $ProjectPath" -ForegroundColor Cyan
Write-Host "🎮 Arquivo do projeto: $ProjectFile" -ForegroundColor Cyan
Write-Host ""
Write-Host "🚀 PRÓXIMOS PASSOS:" -ForegroundColor Yellow
Write-Host "1. Abrir o projeto no Unreal Editor" -ForegroundColor White
Write-Host "2. Compilar o código C++" -ForegroundColor White
Write-Host "3. Configurar Steam App ID" -ForegroundColor White
Write-Host "4. Criar mapas básicos" -ForegroundColor White
Write-Host "5. Implementar sistema de networking" -ForegroundColor White
Write-Host ""
Write-Host "💡 Para abrir o projeto:" -ForegroundColor Yellow
Write-Host "   `"$UE5Path\Engine\Binaries\Win64\UnrealEditor.exe`" `"$ProjectFile`"" -ForegroundColor Gray
Write-Host ""
Write-Host "🎯 READY TO DEVELOP IN UE5! 🎯" -ForegroundColor Green
