// Copyright Tactical Nexus Team. All Rights Reserved.

#include "TacticalNexusGameMode.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/GameStateBase.h"
#include "../Characters/TacticalNexusCharacter.h"
#include "../TacticalNexusUE5.h"

ATacticalNexusGameMode::ATacticalNexusGameMode()
{
    PrimaryActorTick.bCanEverTick = true;

    // Set default pawn class
    DefaultPawnClass = ATacticalNexusCharacter::StaticClass();

    // Initialize match state
    CurrentPhase = EGamePhase::WarmUp;
    CurrentRound = 0;
    RemainingTime = 0.0f;
    bIsOvertime = false;
    bSidesSwapped = false;
    bMatchStarted = false;
    bRoundInProgress = false;
    PhaseStartTime = 0.0f;
    bBombPlanted = false;
    BombLocation = FVector::ZeroVector;
    BombPlanter = nullptr;

    // Initialize team scores
    TerroristScore = FTeamScore();
    CounterTerroristScore = FTeamScore();

    // Initialize consecutive losses
    TerroristConsecutiveLosses = 0;
    CounterTerroristConsecutiveLosses = 0;

    UE_LOG(LogTacticalNexus, Warning, TEXT("🎮 Tactical Nexus Game Mode Initialized - CS2 Style!"));
}

void ATacticalNexusGameMode::BeginPlay()
{
    Super::BeginPlay();
    
    UE_LOG(LogTacticalNexus, Warning, TEXT("🚀 Starting Tactical Nexus Match - MR12 Format"));
    
    // Start with warm-up phase
    StartWarmUp();
}

void ATacticalNexusGameMode::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    UpdateTimer(DeltaTime);
    CheckRoundEndConditions();
    CheckMatchEndConditions();
}

void ATacticalNexusGameMode::StartWarmUp()
{
    UE_LOG(LogTacticalNexus, Log, TEXT("🔥 Starting Warm-Up Phase"));
    
    ChangeGamePhase(EGamePhase::WarmUp);
    RemainingTime = MatchConfig.WarmUpTime;
    
    // Set timer to automatically start match after warm-up
    GetWorldTimerManager().SetTimer(
        PhaseTimerHandle,
        this,
        &ATacticalNexusGameMode::StartMatch,
        MatchConfig.WarmUpTime,
        false
    );
}

void ATacticalNexusGameMode::StartMatch()
{
    UE_LOG(LogTacticalNexus, Warning, TEXT("⚔️ Starting Competitive Match - MR12"));
    
    bMatchStarted = true;
    CurrentRound = 1;
    
    // Reset team scores
    TerroristScore = FTeamScore();
    CounterTerroristScore = FTeamScore();
    TerroristConsecutiveLosses = 0;
    CounterTerroristConsecutiveLosses = 0;
    
    StartRound();
}

void ATacticalNexusGameMode::StartRound()
{
    UE_LOG(LogTacticalNexus, Log, TEXT("🎯 Starting Round %d"), CurrentRound);
    
    bRoundInProgress = true;
    bBombPlanted = false;
    BombLocation = FVector::ZeroVector;
    BombPlanter = nullptr;
    
    // Start freeze time
    ChangeGamePhase(EGamePhase::FreezeTime);
    RemainingTime = MatchConfig.FreezeTime;
    
    // Reset player states
    ResetPlayerStates();
    RespawnAllPlayers();
    InitializeRoundEconomy();
    
    // Set timer for round start
    GetWorldTimerManager().SetTimer(
        PhaseTimerHandle,
        [this]()
        {
            ChangeGamePhase(EGamePhase::RoundActive);
            RemainingTime = MatchConfig.RoundTime;
            
            // Set timer for round end (time expiry)
            GetWorldTimerManager().SetTimer(
                RoundTimerHandle,
                [this]()
                {
                    if (bRoundInProgress)
                    {
                        EndRound(ERoundEndReason::TimeExpired, ETeam::CounterTerrorist);
                    }
                },
                MatchConfig.RoundTime,
                false
            );
        },
        MatchConfig.FreezeTime,
        false
    );
}

void ATacticalNexusGameMode::EndRound(ERoundEndReason Reason, ETeam WinningTeam)
{
    if (!bRoundInProgress) return;
    
    UE_LOG(LogTacticalNexus, Warning, TEXT("🏁 Round %d Ended - Winner: %s, Reason: %s"), 
        CurrentRound, 
        WinningTeam == ETeam::Terrorist ? TEXT("Terrorists") : TEXT("Counter-Terrorists"),
        *UEnum::GetValueAsString(Reason));
    
    bRoundInProgress = false;
    GetWorldTimerManager().ClearTimer(RoundTimerHandle);
    
    // Update team scores
    if (WinningTeam == ETeam::Terrorist)
    {
        TerroristScore.RoundsWon++;
        TerroristScore.ConsecutiveRounds++;
        CounterTerroristScore.ConsecutiveRounds = 0;
        CounterTerroristConsecutiveLosses++;
        TerroristConsecutiveLosses = 0;
    }
    else if (WinningTeam == ETeam::CounterTerrorist)
    {
        CounterTerroristScore.RoundsWon++;
        CounterTerroristScore.ConsecutiveRounds++;
        TerroristScore.ConsecutiveRounds = 0;
        TerroristConsecutiveLosses++;
        CounterTerroristConsecutiveLosses = 0;
    }
    
    // Process economy
    ProcessRoundEconomy(Reason, WinningTeam);
    
    // Broadcast round end event
    OnRoundEnd.Broadcast(Reason, WinningTeam, CurrentRound);
    
    // Check for half-time (after round 12 in MR12)
    if (CurrentRound == 12 && !bSidesSwapped)
    {
        ChangeGamePhase(EGamePhase::HalfTime);
        SwapSides();
        
        GetWorldTimerManager().SetTimer(
            PhaseTimerHandle,
            [this]()
            {
                CurrentRound++;
                StartRound();
            },
            15.0f, // Half-time duration
            false
        );
    }
    else
    {
        // Check for match end
        if (CheckMatchEndConditions())
        {
            return; // Match ended
        }
        
        // Start next round
        ChangeGamePhase(EGamePhase::PostRound);
        
        GetWorldTimerManager().SetTimer(
            PhaseTimerHandle,
            [this]()
            {
                CurrentRound++;
                StartRound();
            },
            5.0f, // Post-round duration
            false
        );
    }
}

void ATacticalNexusGameMode::SwapSides()
{
    UE_LOG(LogTacticalNexus, Warning, TEXT("🔄 Swapping Sides - Half Time"));
    
    bSidesSwapped = true;
    
    // Swap team assignments for all players
    for (auto& TeamPair : TeamPlayers)
    {
        ETeam CurrentTeam = TeamPair.Key;
        TArray<APlayerController*>& Players = TeamPair.Value;
        
        ETeam NewTeam = (CurrentTeam == ETeam::Terrorist) ? ETeam::CounterTerrorist : ETeam::Terrorist;
        
        for (APlayerController* PC : Players)
        {
            if (PC && PC->GetPawn())
            {
                ATacticalNexusCharacter* Character = Cast<ATacticalNexusCharacter>(PC->GetPawn());
                if (Character)
                {
                    Character->SetPlayerTeam(NewTeam == ETeam::Terrorist ? EPlayerTeam::Terrorist : EPlayerTeam::CounterTerrorist);
                }
            }
        }
    }
    
    // Swap the team arrays
    TArray<APlayerController*> TempTerrorists = TeamPlayers[ETeam::Terrorist];
    TeamPlayers[ETeam::Terrorist] = TeamPlayers[ETeam::CounterTerrorist];
    TeamPlayers[ETeam::CounterTerrorist] = TempTerrorists;
    
    // Swap scores
    FTeamScore TempScore = TerroristScore;
    TerroristScore = CounterTerroristScore;
    CounterTerroristScore = TempScore;
}

void ATacticalNexusGameMode::ProcessRoundEconomy(ERoundEndReason Reason, ETeam WinningTeam)
{
    // CS2-style economy system
    int32 WinBonus = 3250; // Standard round win bonus
    int32 LossBonus = 1400; // Base loss bonus
    int32 BombPlantBonus = 800; // Bonus for planting bomb
    
    // Award money to winning team
    AwardTeamMoney(WinningTeam, WinBonus);
    
    // Award loss bonus to losing team (with consecutive loss scaling)
    ETeam LosingTeam = (WinningTeam == ETeam::Terrorist) ? ETeam::CounterTerrorist : ETeam::Terrorist;
    int32 LossBonusAmount = CalculateRoundLossBonusMoney(LosingTeam);
    AwardTeamMoney(LosingTeam, LossBonusAmount);
    
    // Award bomb plant bonus if applicable
    if (bBombPlanted && BombPlanter)
    {
        // Award bomb plant bonus to terrorist team
        AwardTeamMoney(ETeam::Terrorist, BombPlantBonus);
    }
    
    UE_LOG(LogTacticalNexus, Log, TEXT("💰 Economy processed - Win bonus: %d, Loss bonus: %d"), WinBonus, LossBonusAmount);
}

int32 ATacticalNexusGameMode::CalculateRoundLossBonusMoney(ETeam Team) const
{
    int32 ConsecutiveLosses = (Team == ETeam::Terrorist) ? TerroristConsecutiveLosses : CounterTerroristConsecutiveLosses;
    
    // CS2 loss bonus scaling: 1400, 1900, 2400, 2900, 3400 (max)
    int32 LossBonus = 1400 + (ConsecutiveLosses * 500);
    return FMath::Min(LossBonus, 3400); // Cap at 3400
}

void ATacticalNexusGameMode::AwardTeamMoney(ETeam Team, int32 Amount)
{
    TArray<APlayerController*> TeamPlayerList = GetTeamPlayers(Team);
    
    for (APlayerController* PC : TeamPlayerList)
    {
        if (PC && PC->GetPawn())
        {
            ATacticalNexusCharacter* Character = Cast<ATacticalNexusCharacter>(PC->GetPawn());
            if (Character)
            {
                Character->AddMoney(Amount);
            }
        }
    }
}

void ATacticalNexusGameMode::OnBombPlanted(FVector Location, APlayerController* Planter)
{
    UE_LOG(LogTacticalNexus, Warning, TEXT("💣 Bomb Planted at location: %s"), *Location.ToString());
    
    bBombPlanted = true;
    BombLocation = Location;
    BombPlanter = Planter;
    
    // Extend round time for bomb timer (40 seconds in CS2)
    RemainingTime = 40.0f;
    
    GetWorldTimerManager().ClearTimer(RoundTimerHandle);
    GetWorldTimerManager().SetTimer(
        RoundTimerHandle,
        this,
        &ATacticalNexusGameMode::OnBombExploded,
        40.0f,
        false
    );
}

void ATacticalNexusGameMode::OnBombDefused(APlayerController* Defuser)
{
    UE_LOG(LogTacticalNexus, Warning, TEXT("🛡️ Bomb Defused by Counter-Terrorist"));
    
    EndRound(ERoundEndReason::BombDefused, ETeam::CounterTerrorist);
}

void ATacticalNexusGameMode::OnBombExploded()
{
    UE_LOG(LogTacticalNexus, Warning, TEXT("💥 Bomb Exploded - Terrorists Win"));
    
    EndRound(ERoundEndReason::BombExploded, ETeam::Terrorist);
}

bool ATacticalNexusGameMode::CheckMatchEndConditions()
{
    int32 RoundsToWin = GetRoundsToWin();
    
    // Check for standard match win (13 rounds in MR12)
    if (TerroristScore.RoundsWon >= RoundsToWin)
    {
        EndMatch(ETeam::Terrorist);
        return true;
    }
    else if (CounterTerroristScore.RoundsWon >= RoundsToWin)
    {
        EndMatch(ETeam::CounterTerrorist);
        return true;
    }
    
    // Check for overtime conditions
    if (CurrentRound >= MatchConfig.MaxRounds && 
        TerroristScore.RoundsWon == CounterTerroristScore.RoundsWon)
    {
        if (MatchConfig.bEnableOvertimeRounds)
        {
            // Start overtime (MR3 format)
            bIsOvertime = true;
            UE_LOG(LogTacticalNexus, Warning, TEXT("⏰ Match going to Overtime - MR3"));
        }
        else
        {
            EndMatch(ETeam::None); // Draw
            return true;
        }
    }
    
    return false;
}

int32 ATacticalNexusGameMode::GetRoundsToWin() const
{
    if (bIsOvertime)
    {
        return (MatchConfig.MaxRounds / 2) + (MatchConfig.OvertimeMaxRounds / 2) + 1; // 16 rounds in MR12 + MR3 overtime
    }
    else
    {
        return (MatchConfig.MaxRounds / 2) + 1; // 13 rounds in MR12
    }
}

void ATacticalNexusGameMode::EndMatch(ETeam WinningTeam)
{
    UE_LOG(LogTacticalNexus, Warning, TEXT("🏆 Match Ended - Winner: %s"), 
        WinningTeam == ETeam::Terrorist ? TEXT("Terrorists") : 
        WinningTeam == ETeam::CounterTerrorist ? TEXT("Counter-Terrorists") : TEXT("Draw"));
    
    ChangeGamePhase(EGamePhase::MatchEnd);
    bMatchStarted = false;
    
    FTeamScore FinalScore = (WinningTeam == ETeam::Terrorist) ? TerroristScore : CounterTerroristScore;
    OnMatchEnd.Broadcast(WinningTeam, FinalScore);
}

void ATacticalNexusGameMode::ChangeGamePhase(EGamePhase NewPhase)
{
    if (CurrentPhase != NewPhase)
    {
        CurrentPhase = NewPhase;
        PhaseStartTime = GetWorld()->GetTimeSeconds();
        OnGamePhaseChanged.Broadcast(NewPhase);
        
        UE_LOG(LogTacticalNexus, Log, TEXT("🔄 Game Phase Changed to: %s"), *UEnum::GetValueAsString(NewPhase));
    }
}

void ATacticalNexusGameMode::UpdateTimer(float DeltaTime)
{
    if (CurrentPhase == EGamePhase::WarmUp || 
        CurrentPhase == EGamePhase::FreezeTime || 
        CurrentPhase == EGamePhase::RoundActive)
    {
        RemainingTime = FMath::Max(0.0f, RemainingTime - DeltaTime);
    }
}

bool ATacticalNexusGameMode::IsMatchActive() const
{
    return bMatchStarted && CurrentPhase != EGamePhase::MatchEnd;
}

bool ATacticalNexusGameMode::IsRoundActive() const
{
    return bRoundInProgress && CurrentPhase == EGamePhase::RoundActive;
}

bool ATacticalNexusGameMode::IsBuyTimeActive() const
{
    return CurrentPhase == EGamePhase::FreezeTime || 
           (CurrentPhase == EGamePhase::RoundActive && RemainingTime > (MatchConfig.RoundTime - MatchConfig.BuyTime));
}
