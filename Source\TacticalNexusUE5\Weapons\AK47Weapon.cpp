// Copyright Tactical Nexus Team. All Rights Reserved.

#include "AK47Weapon.h"
#include "Engine/Engine.h"
#include "Kismet/GameplayStatics.h"
#include "../Characters/TacticalNexusCharacter.h"
#include "../TacticalNexusUE5.h"

AAK47Weapon::AAK47Weapon()
{
    // Initialize AK-47 specific properties
    HeadshotMultiplier = 4.0f; // 144 damage headshot (36 * 4)
    ArmorPenetration = 0.775f; // 77.5% armor penetration
    MovementInaccuracyMultiplier = 3.0f;
    CrouchAccuracyBonus = 0.5f;
    FirstShotAccuracy = 0.95f;
    
    bFirstShot = true;
    LastFireTime = 0.0f;
    ConsecutiveShots = 0;

    // Initialize AK-47 stats
    InitializeAK47Stats();
    SetupAK47RecoilPattern();

    UE_LOG(LogTacticalNexus, Log, TEXT("🔫 AK-47 Weapon Initialized - CS2 Style"));
}

void AAK47Weapon::BeginPlay()
{
    Super::BeginPlay();
    
    // Override base weapon sounds with AK-47 specific sounds
    if (AK47FireSound)
    {
        FireSound = AK47FireSound;
    }
    
    if (AK47ReloadSound)
    {
        ReloadSound = AK47ReloadSound;
    }
    
    if (AK47MuzzleFlash)
    {
        MuzzleFlashVFX = AK47MuzzleFlash;
    }
}

void AAK47Weapon::InitializeAK47Stats()
{
    // CS2 accurate AK-47 stats
    WeaponStats.WeaponName = TEXT("AK-47");
    WeaponStats.WeaponType = EWeaponType::Rifle;
    WeaponStats.Damage = 36; // Base damage
    WeaponStats.FireRate = 600.0f; // 600 RPM
    WeaponStats.Range = 8192.0f; // Long range
    WeaponStats.Accuracy = 0.73f; // Base accuracy
    WeaponStats.MagazineSize = 30;
    WeaponStats.ReloadTime = 2.5f;
    WeaponStats.Price = 2700; // CS2 price
    WeaponStats.bIsAutomatic = true;

    // Update derived values
    TimeBetweenShots = 60.0f / WeaponStats.FireRate; // 0.1 seconds between shots
    CurrentAmmo = WeaponStats.MagazineSize;
    ReserveAmmo = 90; // 3 extra magazines
}

void AAK47Weapon::SetupAK47RecoilPattern()
{
    // CS2 accurate AK-47 recoil pattern (30 shots)
    AK47RecoilPattern.Empty();
    
    // First 10 shots - vertical climb
    AK47RecoilPattern.Add(FVector2D(0.0f, 18.0f));    // Shot 1
    AK47RecoilPattern.Add(FVector2D(-2.0f, 32.0f));   // Shot 2
    AK47RecoilPattern.Add(FVector2D(2.0f, 42.0f));    // Shot 3
    AK47RecoilPattern.Add(FVector2D(-1.0f, 48.0f));   // Shot 4
    AK47RecoilPattern.Add(FVector2D(3.0f, 52.0f));    // Shot 5
    AK47RecoilPattern.Add(FVector2D(-4.0f, 55.0f));   // Shot 6
    AK47RecoilPattern.Add(FVector2D(2.0f, 57.0f));    // Shot 7
    AK47RecoilPattern.Add(FVector2D(-3.0f, 58.0f));   // Shot 8
    AK47RecoilPattern.Add(FVector2D(5.0f, 59.0f));    // Shot 9
    AK47RecoilPattern.Add(FVector2D(-6.0f, 60.0f));   // Shot 10
    
    // Shots 11-20 - horizontal spray pattern
    AK47RecoilPattern.Add(FVector2D(-8.0f, 58.0f));   // Shot 11
    AK47RecoilPattern.Add(FVector2D(-12.0f, 56.0f));  // Shot 12
    AK47RecoilPattern.Add(FVector2D(-15.0f, 54.0f));  // Shot 13
    AK47RecoilPattern.Add(FVector2D(-18.0f, 52.0f));  // Shot 14
    AK47RecoilPattern.Add(FVector2D(-20.0f, 50.0f));  // Shot 15
    AK47RecoilPattern.Add(FVector2D(-22.0f, 48.0f));  // Shot 16
    AK47RecoilPattern.Add(FVector2D(-20.0f, 46.0f));  // Shot 17
    AK47RecoilPattern.Add(FVector2D(-18.0f, 44.0f));  // Shot 18
    AK47RecoilPattern.Add(FVector2D(-15.0f, 42.0f));  // Shot 19
    AK47RecoilPattern.Add(FVector2D(-12.0f, 40.0f));  // Shot 20
    
    // Shots 21-30 - right side spray
    AK47RecoilPattern.Add(FVector2D(-8.0f, 38.0f));   // Shot 21
    AK47RecoilPattern.Add(FVector2D(-4.0f, 36.0f));   // Shot 22
    AK47RecoilPattern.Add(FVector2D(2.0f, 34.0f));    // Shot 23
    AK47RecoilPattern.Add(FVector2D(8.0f, 32.0f));    // Shot 24
    AK47RecoilPattern.Add(FVector2D(14.0f, 30.0f));   // Shot 25
    AK47RecoilPattern.Add(FVector2D(18.0f, 28.0f));   // Shot 26
    AK47RecoilPattern.Add(FVector2D(22.0f, 26.0f));   // Shot 27
    AK47RecoilPattern.Add(FVector2D(20.0f, 24.0f));   // Shot 28
    AK47RecoilPattern.Add(FVector2D(18.0f, 22.0f));   // Shot 29
    AK47RecoilPattern.Add(FVector2D(15.0f, 20.0f));   // Shot 30
    
    // Set the recoil pattern in the base weapon
    RecoilPattern.RecoilPoints = AK47RecoilPattern;
    RecoilPattern.RecoilMultiplier = 1.0f;
    RecoilPattern.RecoveryRate = 8.0f; // Fast recovery for skilled players
}

void AAK47Weapon::StartFire()
{
    if (!CanFire())
    {
        return;
    }

    // Reset recoil if enough time has passed (first shot accuracy)
    float CurrentTime = GetWorld()->GetTimeSeconds();
    if (CurrentTime - LastFireTime > 0.35f) // CS2 first shot reset time
    {
        bFirstShot = true;
        CurrentRecoilIndex = 0;
        CurrentRecoilOffset = FVector2D::ZeroVector;
        ConsecutiveShots = 0;
    }

    Super::StartFire();
}

void AAK47Weapon::Fire()
{
    if (!CanFire())
    {
        return;
    }

    // Update firing state
    LastFireTime = GetWorld()->GetTimeSeconds();
    ConsecutiveShots++;
    
    // Consume ammo
    CurrentAmmo--;

    // Perform line trace with AK-47 specific calculations
    FVector StartLocation;
    FVector EndLocation;
    FRotator FireRotation;

    if (OwnerCharacter)
    {
        FVector CameraLocation;
        FRotator CameraRotation;
        OwnerCharacter->GetActorEyesViewPoint(CameraLocation, CameraRotation);

        // Calculate AK-47 specific spread and recoil
        FVector RecoilOffset = CalculateRecoil();
        FVector SpreadOffset = CalculateAK47Spread();
        
        FireRotation = CameraRotation + FRotator(
            RecoilOffset.Y + SpreadOffset.Y,
            RecoilOffset.X + SpreadOffset.X,
            0.0f
        );

        StartLocation = CameraLocation;
        EndLocation = StartLocation + (FireRotation.Vector() * WeaponStats.Range);
    }

    // Perform line trace
    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.AddIgnoredActor(this);
    QueryParams.AddIgnoredActor(GetOwner());
    QueryParams.bTraceComplex = true;

    bool bHit = GetWorld()->LineTraceSingleByChannel(
        HitResult,
        StartLocation,
        EndLocation,
        ECC_Visibility,
        QueryParams
    );

    if (bHit)
    {
        ProcessHit(HitResult);
    }

    // Play effects
    MulticastPlayFireEffects();

    // Update recoil pattern
    if (CurrentRecoilIndex < AK47RecoilPattern.Num())
    {
        FVector2D RecoilVector = AK47RecoilPattern[CurrentRecoilIndex];
        
        // Apply first shot accuracy bonus
        if (bFirstShot)
        {
            RecoilVector *= 0.3f; // Reduce first shot recoil
            bFirstShot = false;
        }
        
        CurrentRecoilOffset += RecoilVector * RecoilPattern.RecoilMultiplier;
        CurrentRecoilIndex++;
    }

    UE_LOG(LogTacticalNexus, VeryVerbose, TEXT("🔫 AK-47 Fired - Shot %d, Recoil: %s"), 
        ConsecutiveShots, *CurrentRecoilOffset.ToString());
}

void AAK47Weapon::ProcessHit(const FHitResult& HitResult)
{
    if (!HitResult.GetActor())
    {
        return;
    }

    // Calculate AK-47 specific damage
    float DamageAmount = CalculateAK47Damage(HitResult);
    
    UE_LOG(LogTacticalNexus, Log, TEXT("🎯 AK-47 Hit - Damage: %.1f, Location: %s"), 
        DamageAmount, *HitResult.BoneName.ToString());

    // Apply damage
    UGameplayStatics::ApplyPointDamage(
        HitResult.GetActor(),
        DamageAmount,
        HitResult.Location,
        HitResult,
        OwnerCharacter ? OwnerCharacter->GetController() : nullptr,
        this,
        UDamageType::StaticClass()
    );

    // Spawn impact effects
    if (ImpactVFX)
    {
        UGameplayStatics::SpawnEmitterAtLocation(
            GetWorld(),
            ImpactVFX,
            HitResult.Location,
            HitResult.Normal.Rotation()
        );
    }
}

FVector AAK47Weapon::CalculateAK47Spread()
{
    float SpreadAmount = (1.0f - WeaponStats.Accuracy);
    
    // Apply first shot accuracy
    if (bFirstShot)
    {
        SpreadAmount *= (1.0f - FirstShotAccuracy);
    }
    
    // Modify spread based on player state
    if (OwnerCharacter)
    {
        // Increase spread when moving (CS2 style)
        float VelocitySize = OwnerCharacter->GetVelocity().Size();
        if (VelocitySize > 10.0f)
        {
            float MovementFactor = FMath::Clamp(VelocitySize / 250.0f, 0.0f, 1.0f);
            SpreadAmount *= (1.0f + MovementFactor * MovementInaccuracyMultiplier);
        }
        
        // Decrease spread when crouching
        if (OwnerCharacter->bIsCrouched)
        {
            SpreadAmount *= CrouchAccuracyBonus;
        }
        
        // Increase spread with consecutive shots
        float ConsecutiveShotsPenalty = FMath::Min(ConsecutiveShots * 0.1f, 2.0f);
        SpreadAmount *= (1.0f + ConsecutiveShotsPenalty);
    }

    return FVector(
        FMath::RandRange(-SpreadAmount, SpreadAmount),
        FMath::RandRange(-SpreadAmount, SpreadAmount),
        0.0f
    );
}

float AAK47Weapon::CalculateAK47Damage(const FHitResult& HitResult)
{
    float BaseDamage = WeaponStats.Damage;
    
    // Check for headshot
    bool bIsHeadshot = (HitResult.BoneName == TEXT("head") || 
                       HitResult.BoneName == TEXT("Head") ||
                       HitResult.BoneName.ToString().Contains(TEXT("head")));
    
    if (bIsHeadshot)
    {
        BaseDamage *= HeadshotMultiplier; // 144 damage headshot
        UE_LOG(LogTacticalNexus, Warning, TEXT("💀 AK-47 HEADSHOT! Damage: %.1f"), BaseDamage);
    }
    
    // Apply armor penetration if target has armor
    ATacticalNexusCharacter* TargetCharacter = Cast<ATacticalNexusCharacter>(HitResult.GetActor());
    if (TargetCharacter && TargetCharacter->Armor > 0.0f)
    {
        // CS2 armor calculation
        float ArmorDamageReduction = (1.0f - ArmorPenetration) * 0.5f;
        BaseDamage *= (1.0f - ArmorDamageReduction);
        
        UE_LOG(LogTacticalNexus, VeryVerbose, TEXT("🛡️ Armor penetration applied - Final damage: %.1f"), BaseDamage);
    }
    
    // Distance falloff (minimal for AK-47)
    float Distance = FVector::Dist(GetActorLocation(), HitResult.Location);
    if (Distance > 1000.0f) // Start falloff after 10 meters
    {
        float FalloffFactor = FMath::Clamp(1.0f - ((Distance - 1000.0f) / 7000.0f), 0.75f, 1.0f);
        BaseDamage *= FalloffFactor;
    }
    
    return BaseDamage;
}
