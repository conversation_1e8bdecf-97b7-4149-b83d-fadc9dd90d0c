// Copyright Tactical Nexus Team. All Rights Reserved.
// REVOLUTIONARY GAMEPLAY SYSTEMS - BEYOND CS2

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Engine/DataTable.h"
#include "Components/ActorComponent.h"
#include "Net/UnrealNetwork.h"
#include "AdvancedGameplaySystem.generated.h"

UENUM(BlueprintType)
enum class EAdvancedGameMode : uint8
{
    Competitive        UMETA(DisplayName = "Competitive 5v5"),
    TacticalOps        UMETA(DisplayName = "Tactical Operations 10v10"),
    Elimination        UMETA(DisplayName = "Elimination Battle Royale"),
    Siege             UMETA(DisplayName = "Siege Mode"),
    Infiltration      UMETA(DisplayName = "Infiltration"),
    Demolition        UMETA(DisplayName = "Demolition"),
    Hostage           UMETA(DisplayName = "Hostage Rescue"),
    Assassination     UMETA(DisplayName = "VIP Assassination"),
    Territory         UMETA(DisplayName = "Territory Control"),
    Hardcore          UMETA(DisplayName = "Hardcore Realism")
};

UENUM(BlueprintType)
enum class EWeatherCondition : uint8
{
    Clear             UMETA(DisplayName = "Clear"),
    Rain              UMETA(DisplayName = "Rain"),
    Snow              UMETA(DisplayName = "Snow"),
    Fog               UMETA(DisplayName = "Fog"),
    Sandstorm         UMETA(DisplayName = "Sandstorm"),
    Night             UMETA(DisplayName = "Night"),
    Dawn              UMETA(DisplayName = "Dawn"),
    Dusk              UMETA(DisplayName = "Dusk")
};

UENUM(BlueprintType)
enum class EAdvancedSkill : uint8
{
    Marksmanship      UMETA(DisplayName = "Marksmanship"),
    Stealth           UMETA(DisplayName = "Stealth"),
    Explosives        UMETA(DisplayName = "Explosives"),
    Medical           UMETA(DisplayName = "Medical"),
    Engineering       UMETA(DisplayName = "Engineering"),
    Leadership        UMETA(DisplayName = "Leadership"),
    Intelligence      UMETA(DisplayName = "Intelligence"),
    Survival          UMETA(DisplayName = "Survival")
};

USTRUCT(BlueprintType)
struct FAdvancedPlayerSkills
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skills")
    TMap<EAdvancedSkill, int32> SkillLevels;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skills")
    int32 SkillPoints;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Skills")
    TArray<FString> UnlockedAbilities;

    FAdvancedPlayerSkills()
    {
        SkillPoints = 0;
        // Initialize all skills at level 1
        SkillLevels.Add(EAdvancedSkill::Marksmanship, 1);
        SkillLevels.Add(EAdvancedSkill::Stealth, 1);
        SkillLevels.Add(EAdvancedSkill::Explosives, 1);
        SkillLevels.Add(EAdvancedSkill::Medical, 1);
        SkillLevels.Add(EAdvancedSkill::Engineering, 1);
        SkillLevels.Add(EAdvancedSkill::Leadership, 1);
        SkillLevels.Add(EAdvancedSkill::Intelligence, 1);
        SkillLevels.Add(EAdvancedSkill::Survival, 1);
    }
};

USTRUCT(BlueprintType)
struct FDynamicEnvironment
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    EWeatherCondition CurrentWeather;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    float TimeOfDay; // 0-24 hours

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    float WindStrength;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    FVector WindDirection;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    float Temperature; // Celsius

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    float Humidity; // 0-100%

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    float Visibility; // 0-1 (fog/weather effects)

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    bool bDynamicWeather;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    bool bRealtimeLighting;

    FDynamicEnvironment()
    {
        CurrentWeather = EWeatherCondition::Clear;
        TimeOfDay = 12.0f; // Noon
        WindStrength = 0.0f;
        WindDirection = FVector::ForwardVector;
        Temperature = 20.0f;
        Humidity = 50.0f;
        Visibility = 1.0f;
        bDynamicWeather = true;
        bRealtimeLighting = true;
    }
};

USTRUCT(BlueprintType)
struct FAdvancedBallistics
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics")
    float BulletVelocity; // m/s

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics")
    float BulletMass; // grams

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics")
    float DragCoefficient;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics")
    float BallisticCoefficient;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics")
    bool bAffectedByWind;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics")
    bool bAffectedByGravity;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics")
    bool bAffectedByTemperature;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics")
    float PenetrationPower;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ballistics")
    TArray<class UMaterialInterface*> PenetratableMaterials;

    FAdvancedBallistics()
    {
        BulletVelocity = 800.0f; // 800 m/s
        BulletMass = 4.0f; // 4 grams
        DragCoefficient = 0.3f;
        BallisticCoefficient = 0.5f;
        bAffectedByWind = true;
        bAffectedByGravity = true;
        bAffectedByTemperature = true;
        PenetrationPower = 50.0f;
    }
};

USTRUCT(BlueprintType)
struct FAdvancedAI
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI")
    float ReactionTime; // seconds

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI")
    float Accuracy; // 0-1

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI")
    float Aggression; // 0-1

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI")
    float Teamwork; // 0-1

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI")
    float Adaptability; // 0-1

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI")
    TArray<FString> KnownStrategies;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI")
    bool bLearnsFromPlayer;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI")
    bool bUsesAdvancedTactics;

    FAdvancedAI()
    {
        ReactionTime = 0.2f;
        Accuracy = 0.8f;
        Aggression = 0.6f;
        Teamwork = 0.9f;
        Adaptability = 0.7f;
        bLearnsFromPlayer = true;
        bUsesAdvancedTactics = true;
    }
};

UCLASS(Blueprintable, BlueprintType)
class TACTICALNEXUSUE5_API UAdvancedGameplaySystem : public UActorComponent
{
    GENERATED_BODY()

public:
    UAdvancedGameplaySystem();

protected:
    virtual void BeginPlay() override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // Advanced Gameplay Functions
    UFUNCTION(BlueprintCallable, Category = "Advanced Gameplay")
    void InitializeAdvancedSystems();

    UFUNCTION(BlueprintCallable, Category = "Advanced Gameplay")
    void UpdateDynamicEnvironment(float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "Advanced Gameplay")
    void ProcessAdvancedBallistics(const FVector& StartLocation, const FVector& EndLocation, const FAdvancedBallistics& BallisticsData);

    UFUNCTION(BlueprintCallable, Category = "Advanced Gameplay")
    void UpdatePlayerSkills(class ATacticalNexusCharacter* Player, EAdvancedSkill Skill, int32 Experience);

    // Environmental Systems
    UFUNCTION(BlueprintCallable, Category = "Environment")
    void ChangeWeather(EWeatherCondition NewWeather, float TransitionTime = 5.0f);

    UFUNCTION(BlueprintCallable, Category = "Environment")
    void SetTimeOfDay(float Hours);

    UFUNCTION(BlueprintCallable, Category = "Environment")
    void UpdateLighting();

    UFUNCTION(BlueprintCallable, Category = "Environment")
    void UpdateWeatherEffects();

    // AI Systems
    UFUNCTION(BlueprintCallable, Category = "AI")
    void UpdateAIBehavior(class ATacticalNexusCharacter* AICharacter);

    UFUNCTION(BlueprintCallable, Category = "AI")
    void ProcessAILearning(const FString& PlayerAction, const FString& Result);

    // Ballistics Systems
    UFUNCTION(BlueprintCallable, Category = "Ballistics")
    FVector CalculateBulletTrajectory(const FVector& StartLocation, const FVector& InitialVelocity, float Time, const FAdvancedBallistics& BallisticsData);

    UFUNCTION(BlueprintCallable, Category = "Ballistics")
    bool ProcessBulletPenetration(const FHitResult& HitResult, const FAdvancedBallistics& BallisticsData, FVector& NewDirection, float& RemainingPower);

    // Skill System
    UFUNCTION(BlueprintCallable, Category = "Skills")
    void LevelUpSkill(class ATacticalNexusCharacter* Player, EAdvancedSkill Skill);

    UFUNCTION(BlueprintCallable, Category = "Skills")
    void UnlockAbility(class ATacticalNexusCharacter* Player, const FString& AbilityName);

    UFUNCTION(BlueprintCallable, Category = "Skills")
    bool CanUseAbility(class ATacticalNexusCharacter* Player, const FString& AbilityName);

protected:
    // Advanced Systems Data
    UPROPERTY(Replicated, BlueprintReadOnly, Category = "Advanced Systems")
    FDynamicEnvironment EnvironmentData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced Systems")
    FAdvancedAI DefaultAISettings;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced Systems")
    TMap<FString, FAdvancedBallistics> WeaponBallistics;

    // Environmental Effects
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    class UNiagaraSystem* RainEffect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    class UNiagaraSystem* SnowEffect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    class UNiagaraSystem* FogEffect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    class UNiagaraSystem* SandstormEffect;

    // Lighting System
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting")
    class ADirectionalLight* SunLight;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting")
    class ASkyLight* SkyLight;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Lighting")
    class AExponentialHeightFog* HeightFog;

    // AI Learning System
    UPROPERTY(BlueprintReadOnly, Category = "AI")
    TMap<FString, int32> PlayerActionDatabase;

    UPROPERTY(BlueprintReadOnly, Category = "AI")
    TArray<FString> LearnedStrategies;

private:
    // Internal systems
    float EnvironmentUpdateTimer;
    float AIUpdateTimer;
    float BallisticsUpdateTimer;

    // Weather transition
    bool bWeatherTransitioning;
    EWeatherCondition TargetWeather;
    float WeatherTransitionTime;
    float WeatherTransitionProgress;

    // Performance optimization
    int32 MaxSimultaneousBallistics;
    TArray<struct FBallisticsCalculation> ActiveBallistics;
};
