// Copyright Tactical Nexus Team. All Rights Reserved.
// ULTRA-REALISTIC BALLISTICS SYSTEM - BEYOND REAL LIFE

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Engine/DataTable.h"
#include "Particles/ParticleSystem.h"
#include "NiagaraSystem.h"
#include "NiagaraComponent.h"
#include "PhysicalMaterials/PhysicalMaterial.h"
#include "Net/UnrealNetwork.h"
#include "UltraRealisticBallistics.generated.h"

UENUM(BlueprintType)
enum class EBulletType : uint8
{
    FullMetalJacket     UMETA(DisplayName = "Full Metal Jacket"),
    HollowPoint         UMETA(DisplayName = "Hollow Point"),
    ArmorPiercing       UMETA(DisplayName = "Armor Piercing"),
    Incendiary          UMETA(DisplayName = "Incendiary"),
    Explosive           UMETA(DisplayName = "Explosive"),
    Tracer              UMETA(DisplayName = "Tracer"),
    Subsonic            UMETA(DisplayName = "Subsonic"),
    Frangible           UMETA(DisplayName = "Frangible")
};

UENUM(BlueprintType)
enum class EMaterialType : uint8
{
    Flesh               UMETA(DisplayName = "Flesh"),
    Bone                UMETA(DisplayName = "Bone"),
    Steel               UMETA(DisplayName = "Steel"),
    Aluminum            UMETA(DisplayName = "Aluminum"),
    Concrete            UMETA(DisplayName = "Concrete"),
    Wood                UMETA(DisplayName = "Wood"),
    Glass               UMETA(DisplayName = "Glass"),
    Ceramic             UMETA(DisplayName = "Ceramic"),
    Kevlar              UMETA(DisplayName = "Kevlar"),
    Water               UMETA(DisplayName = "Water"),
    Sand                UMETA(DisplayName = "Sand"),
    Dirt                UMETA(DisplayName = "Dirt")
};

USTRUCT(BlueprintType)
struct FBulletProperties
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bullet Properties")
    EBulletType BulletType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bullet Properties")
    float Mass; // grams

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bullet Properties")
    float Diameter; // mm

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bullet Properties")
    float Length; // mm

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bullet Properties")
    float MuzzleVelocity; // m/s

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bullet Properties")
    float BallisticCoefficient;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bullet Properties")
    float DragCoefficient;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bullet Properties")
    float SectionalDensity;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bullet Properties")
    float HardnessRating; // Brinell hardness

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bullet Properties")
    float ExpansionCoefficient; // For hollow points

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bullet Properties")
    bool bFragments;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bullet Properties")
    int32 FragmentCount;

    FBulletProperties()
    {
        BulletType = EBulletType::FullMetalJacket;
        Mass = 4.0f;
        Diameter = 5.56f;
        Length = 23.0f;
        MuzzleVelocity = 990.0f;
        BallisticCoefficient = 0.307f;
        DragCoefficient = 0.295f;
        SectionalDensity = 0.224f;
        HardnessRating = 15.0f;
        ExpansionCoefficient = 1.0f;
        bFragments = false;
        FragmentCount = 0;
    }
};

USTRUCT(BlueprintType)
struct FMaterialProperties
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Properties")
    EMaterialType MaterialType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Properties")
    float Density; // kg/m³

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Properties")
    float Hardness; // Brinell hardness

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Properties")
    float Thickness; // mm

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Properties")
    float PenetrationResistance;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Properties")
    float RicochetAngle; // degrees

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Properties")
    float EnergyAbsorption; // 0-1

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Properties")
    bool bCanSpall;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Properties")
    bool bCanFragment;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Properties")
    float SpallVelocity;

    FMaterialProperties()
    {
        MaterialType = EMaterialType::Steel;
        Density = 7850.0f;
        Hardness = 200.0f;
        Thickness = 10.0f;
        PenetrationResistance = 100.0f;
        RicochetAngle = 20.0f;
        EnergyAbsorption = 0.3f;
        bCanSpall = true;
        bCanFragment = false;
        SpallVelocity = 300.0f;
    }
};

USTRUCT(BlueprintType)
struct FBallisticTrajectory
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Trajectory")
    TArray<FVector> TrajectoryPoints;

    UPROPERTY(BlueprintReadOnly, Category = "Trajectory")
    TArray<float> VelocityAtPoints;

    UPROPERTY(BlueprintReadOnly, Category = "Trajectory")
    TArray<float> EnergyAtPoints;

    UPROPERTY(BlueprintReadOnly, Category = "Trajectory")
    float TotalFlightTime;

    UPROPERTY(BlueprintReadOnly, Category = "Trajectory")
    float MaxRange;

    UPROPERTY(BlueprintReadOnly, Category = "Trajectory")
    float Drop; // Vertical drop

    UPROPERTY(BlueprintReadOnly, Category = "Trajectory")
    float Drift; // Horizontal drift due to spin

    UPROPERTY(BlueprintReadOnly, Category = "Trajectory")
    float WindDeflection;

    FBallisticTrajectory()
    {
        TotalFlightTime = 0.0f;
        MaxRange = 0.0f;
        Drop = 0.0f;
        Drift = 0.0f;
        WindDeflection = 0.0f;
    }
};

USTRUCT(BlueprintType)
struct FEnvironmentalFactors
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    float Temperature; // Celsius

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    float Humidity; // 0-100%

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    float AirPressure; // hPa

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    float Altitude; // meters above sea level

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    FVector WindVelocity; // m/s

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    float AirDensity; // kg/m³

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    bool bRaining;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    float RainIntensity; // 0-1

    FEnvironmentalFactors()
    {
        Temperature = 15.0f;
        Humidity = 50.0f;
        AirPressure = 1013.25f;
        Altitude = 0.0f;
        WindVelocity = FVector::ZeroVector;
        AirDensity = 1.225f;
        bRaining = false;
        RainIntensity = 0.0f;
    }
};

UCLASS(Blueprintable, BlueprintType, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class TACTICALNEXUSUE5_API UUltraRealisticBallistics : public UActorComponent
{
    GENERATED_BODY()

public:
    UUltraRealisticBallistics();

protected:
    virtual void BeginPlay() override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // Main Ballistics Functions
    UFUNCTION(BlueprintCallable, Category = "Ultra Ballistics")
    FBallisticTrajectory CalculateTrajectory(const FVector& StartLocation, const FVector& TargetLocation, const FBulletProperties& BulletData, const FEnvironmentalFactors& Environment);

    UFUNCTION(BlueprintCallable, Category = "Ultra Ballistics")
    bool FireBullet(const FVector& StartLocation, const FVector& Direction, const FBulletProperties& BulletData, const FEnvironmentalFactors& Environment);

    UFUNCTION(BlueprintCallable, Category = "Ultra Ballistics")
    bool ProcessPenetration(const FHitResult& HitResult, const FBulletProperties& BulletData, const FMaterialProperties& MaterialData, FVector& ExitLocation, FVector& ExitDirection, float& RemainingEnergy);

    UFUNCTION(BlueprintCallable, Category = "Ultra Ballistics")
    bool ProcessRicochet(const FHitResult& HitResult, const FBulletProperties& BulletData, const FMaterialProperties& MaterialData, FVector& RicochetDirection, float& RemainingEnergy);

    // Advanced Physics Calculations
    UFUNCTION(BlueprintCallable, Category = "Physics")
    float CalculateDragForce(float Velocity, float AirDensity, float DragCoefficient, float CrossSectionalArea);

    UFUNCTION(BlueprintCallable, Category = "Physics")
    FVector CalculateWindDeflection(const FVector& BulletVelocity, const FVector& WindVelocity, float FlightTime);

    UFUNCTION(BlueprintCallable, Category = "Physics")
    float CalculateSpinDrift(float Velocity, float FlightTime, float TwistRate);

    UFUNCTION(BlueprintCallable, Category = "Physics")
    float CalculateCoriolisEffect(const FVector& Velocity, float Latitude, float FlightTime);

    UFUNCTION(BlueprintCallable, Category = "Physics")
    float CalculateTemperatureEffect(float Temperature, float MuzzleVelocity);

    // Material Interaction
    UFUNCTION(BlueprintCallable, Category = "Material Interaction")
    FMaterialProperties GetMaterialProperties(UPhysicalMaterial* PhysicalMaterial);

    UFUNCTION(BlueprintCallable, Category = "Material Interaction")
    void ProcessSpalling(const FHitResult& HitResult, const FMaterialProperties& MaterialData, float ImpactEnergy);

    UFUNCTION(BlueprintCallable, Category = "Material Interaction")
    void ProcessFragmentation(const FVector& Location, const FBulletProperties& BulletData, float ImpactEnergy);

    // Visual Effects
    UFUNCTION(BlueprintCallable, Category = "Visual Effects")
    void SpawnBulletTrail(const FVector& StartLocation, const FVector& EndLocation, const FBulletProperties& BulletData);

    UFUNCTION(BlueprintCallable, Category = "Visual Effects")
    void SpawnImpactEffect(const FHitResult& HitResult, const FBulletProperties& BulletData, const FMaterialProperties& MaterialData);

    UFUNCTION(BlueprintCallable, Category = "Visual Effects")
    void SpawnPenetrationEffect(const FVector& EntryPoint, const FVector& ExitPoint, const FMaterialProperties& MaterialData);

    // Sound Effects
    UFUNCTION(BlueprintCallable, Category = "Audio")
    void PlaySupersonicCrack(const FVector& Location, float Velocity);

    UFUNCTION(BlueprintCallable, Category = "Audio")
    void PlayImpactSound(const FHitResult& HitResult, const FMaterialProperties& MaterialData, float ImpactEnergy);

protected:
    // Environmental Data
    UPROPERTY(Replicated, BlueprintReadOnly, Category = "Environment")
    FEnvironmentalFactors CurrentEnvironment;

    // Material Database
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Materials")
    TMap<UPhysicalMaterial*, FMaterialProperties> MaterialDatabase;

    // Visual Effects
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    UNiagaraSystem* BulletTrailEffect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    TMap<EMaterialType, UNiagaraSystem*> ImpactEffects;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    UNiagaraSystem* SparkEffect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    UNiagaraSystem* BloodEffect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    UNiagaraSystem* FragmentationEffect;

    // Audio Effects
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    class USoundCue* SupersonicCrackSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    TMap<EMaterialType, class USoundCue*> ImpactSounds;

    // Performance Settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxSimultaneousBullets;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MaxBulletLifetime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MinVelocityThreshold;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bUseAdvancedPhysics;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bCalculateSpinDrift;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bCalculateCoriolisEffect;

private:
    // Active bullet tracking
    struct FActiveBullet
    {
        FVector Position;
        FVector Velocity;
        FBulletProperties Properties;
        float Energy;
        float FlightTime;
        int32 PenetrationCount;
        bool bIsActive;
    };

    TArray<FActiveBullet> ActiveBullets;

    // Physics constants
    static constexpr float GRAVITY = 9.80665f; // m/s²
    static constexpr float EARTH_ROTATION_RATE = 7.2921159e-5f; // rad/s
    static constexpr float STANDARD_AIR_DENSITY = 1.225f; // kg/m³
    static constexpr float STANDARD_TEMPERATURE = 15.0f; // °C
    static constexpr float STANDARD_PRESSURE = 101325.0f; // Pa

    // Helper functions
    float CalculateAirDensity(float Temperature, float Pressure, float Humidity);
    float CalculateBallisticCoefficient(const FBulletProperties& BulletData);
    FVector IntegrateTrajectory(const FVector& Position, const FVector& Velocity, float DeltaTime, const FBulletProperties& BulletData, const FEnvironmentalFactors& Environment);
    bool CheckCollision(const FVector& StartPos, const FVector& EndPos, FHitResult& HitResult);
};
