// Copyright Tactical Nexus Team. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/SkeletalMeshComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Sound/SoundCue.h"
#include "Particles/ParticleSystem.h"
#include "Engine/DataTable.h"
#include "Net/UnrealNetwork.h"
#include "BaseWeapon.generated.h"

UENUM(BlueprintType)
enum class EWeaponType : uint8
{
    Rifle       UMETA(DisplayName = "Rifle"),
    Pistol      UMETA(DisplayName = "Pistol"),
    Sniper      UMETA(DisplayName = "Sniper"),
    SMG         UMETA(DisplayName = "SMG"),
    Shotgun     UMETA(DisplayName = "Shotgun"),
    Grenade     UMETA(DisplayName = "Grenade"),
    Knife       UMETA(DisplayName = "Knife")
};

UENUM(BlueprintType)
enum class EFireMode : uint8
{
    Single      UMETA(DisplayName = "Single"),
    Burst       UMETA(DisplayName = "Burst"),
    FullAuto    UMETA(DisplayName = "Full Auto")
};

USTRUCT(BlueprintType)
struct FWeaponStats : public FTableRowBase
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    FString WeaponName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    EWeaponType WeaponType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    int32 Damage;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    float FireRate; // Rounds per minute

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    float Range;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    float Accuracy;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    int32 MagazineSize;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    float ReloadTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    int32 Price; // CS2-style pricing

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    bool bIsAutomatic;

    FWeaponStats()
    {
        WeaponName = TEXT("Default Weapon");
        WeaponType = EWeaponType::Rifle;
        Damage = 30;
        FireRate = 600.0f;
        Range = 1000.0f;
        Accuracy = 0.8f;
        MagazineSize = 30;
        ReloadTime = 2.5f;
        Price = 2700;
        bIsAutomatic = true;
    }
};

USTRUCT(BlueprintType)
struct FRecoilPattern
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recoil")
    TArray<FVector2D> RecoilPoints;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recoil")
    float RecoilMultiplier;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recoil")
    float RecoveryRate;

    FRecoilPattern()
    {
        RecoilMultiplier = 1.0f;
        RecoveryRate = 5.0f;
    }
};

UCLASS(Blueprintable, BlueprintType)
class TACTICALNEXUSUE5_API ABaseWeapon : public AActor
{
    GENERATED_BODY()

public:
    ABaseWeapon();

protected:
    virtual void BeginPlay() override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    virtual void Tick(float DeltaTime) override;

    // Weapon Components
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    USkeletalMeshComponent* WeaponMesh;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    UStaticMeshComponent* MuzzleFlashComponent;

    // Weapon Stats
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    FWeaponStats WeaponStats;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon Stats")
    FRecoilPattern RecoilPattern;

    // Current State
    UPROPERTY(ReplicatedUsing = OnRep_CurrentAmmo, BlueprintReadOnly, Category = "Weapon State")
    int32 CurrentAmmo;

    UPROPERTY(Replicated, BlueprintReadOnly, Category = "Weapon State")
    int32 ReserveAmmo;

    UPROPERTY(ReplicatedUsing = OnRep_IsReloading, BlueprintReadOnly, Category = "Weapon State")
    bool bIsReloading;

    UPROPERTY(Replicated, BlueprintReadOnly, Category = "Weapon State")
    bool bIsFiring;

    // Audio
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    USoundCue* FireSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    USoundCue* ReloadSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    USoundCue* EmptySound;

    // VFX
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    UParticleSystem* MuzzleFlashVFX;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    UParticleSystem* ImpactVFX;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
    UParticleSystem* TracerVFX;

    // Weapon Functions
    UFUNCTION(BlueprintCallable, Category = "Weapon")
    virtual void StartFire();

    UFUNCTION(BlueprintCallable, Category = "Weapon")
    virtual void StopFire();

    UFUNCTION(BlueprintCallable, Category = "Weapon")
    virtual void Reload();

    UFUNCTION(BlueprintCallable, Category = "Weapon")
    virtual bool CanFire() const;

    UFUNCTION(BlueprintCallable, Category = "Weapon")
    virtual bool CanReload() const;

    // Replication Functions
    UFUNCTION()
    void OnRep_CurrentAmmo();

    UFUNCTION()
    void OnRep_IsReloading();

    // Server Functions
    UFUNCTION(Server, Reliable, WithValidation)
    void ServerFire();

    UFUNCTION(Server, Reliable, WithValidation)
    void ServerReload();

    // Multicast Functions
    UFUNCTION(NetMulticast, Reliable)
    void MulticastPlayFireEffects();

    UFUNCTION(NetMulticast, Reliable)
    void MulticastPlayReloadEffects();

protected:
    // Internal Functions
    virtual void Fire();
    virtual void ProcessHit(const FHitResult& HitResult);
    virtual void PlayFireEffects();
    virtual void PlayReloadEffects();
    virtual FVector CalculateRecoil();
    virtual FVector CalculateSpread();
    virtual void InitializeWeaponStats();

    // Timers
    FTimerHandle FireTimerHandle;
    FTimerHandle ReloadTimerHandle;

    // Recoil System
    int32 CurrentRecoilIndex;
    float LastFireTime;
    FVector2D CurrentRecoilOffset;

    // Fire Rate Control
    float TimeBetweenShots;
    float LastShotTime;

private:
    // Owner Reference
    UPROPERTY()
    class ATacticalNexusCharacter* OwnerCharacter;

    // Weapon ID for networking
    UPROPERTY(Replicated)
    int32 WeaponID;
};
