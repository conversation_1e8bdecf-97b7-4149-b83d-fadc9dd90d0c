import { EventEmitter } from 'events';
import { CS2EconomySystem, CS2WeaponPrice } from '../gameplay/CS2EconomySystem';

export interface BuyMenuCategory {
    id: string;
    name: string;
    icon: string;
    items: CS2WeaponPrice[];
}

export interface BuyMenuState {
    isOpen: boolean;
    selectedCategory: string;
    playerMoney: number;
    team: 'terrorist' | 'counter_terrorist';
    timeRemaining: number;
    canBuy: boolean;
}

export class CS2BuyMenu extends EventEmitter {
    private economySystem: CS2EconomySystem;
    private menuState: BuyMenuState;
    private categories: Map<string, BuyMenuCategory> = new Map();
    private menuElement?: HTMLElement;
    private currentPlayerId?: string;

    constructor(economySystem: CS2EconomySystem) {
        super();
        this.economySystem = economySystem;
        this.menuState = {
            isOpen: false,
            selectedCategory: 'rifles',
            playerMoney: 0,
            team: 'terrorist',
            timeRemaining: 0,
            canBuy: false
        };
        
        this.initializeCategories();
        this.createMenuHTML();
        this.setupEventListeners();
        console.log('🛒 CS2 Buy Menu inicializado');
    }

    private initializeCategories(): void {
        // Rifles
        this.categories.set('rifles', {
            id: 'rifles',
            name: 'Rifles',
            icon: '🔫',
            items: this.economySystem.getWeaponsByCategory('rifle')
        });

        // Pistolas
        this.categories.set('pistols', {
            id: 'pistols',
            name: 'Pistolas',
            icon: '🔫',
            items: this.economySystem.getWeaponsByCategory('pistol')
        });

        // SMGs
        this.categories.set('smgs', {
            id: 'smgs',
            name: 'SMGs',
            icon: '🔫',
            items: this.economySystem.getWeaponsByCategory('smg')
        });

        // Snipers
        this.categories.set('snipers', {
            id: 'snipers',
            name: 'Snipers',
            icon: '🎯',
            items: this.economySystem.getWeaponsByCategory('sniper')
        });

        // Shotguns
        this.categories.set('shotguns', {
            id: 'shotguns',
            name: 'Shotguns',
            icon: '💥',
            items: this.economySystem.getWeaponsByCategory('shotgun')
        });

        // Granadas
        this.categories.set('grenades', {
            id: 'grenades',
            name: 'Granadas',
            icon: '💣',
            items: this.economySystem.getWeaponsByCategory('grenade')
        });

        // Equipamentos
        this.categories.set('equipment', {
            id: 'equipment',
            name: 'Equipamentos',
            icon: '🛡️',
            items: this.economySystem.getWeaponsByCategory('equipment')
        });
    }

    private createMenuHTML(): void {
        // Remove menu existente se houver
        const existingMenu = document.getElementById('cs2-buy-menu');
        if (existingMenu) {
            existingMenu.remove();
        }

        // Cria container principal
        this.menuElement = document.createElement('div');
        this.menuElement.id = 'cs2-buy-menu';
        this.menuElement.className = 'cs2-buy-menu hidden';
        
        this.menuElement.innerHTML = `
            <div class="buy-menu-overlay">
                <div class="buy-menu-container">
                    <div class="buy-menu-header">
                        <h2>🛒 Buy Menu</h2>
                        <div class="player-info">
                            <span class="money">$<span id="player-money">0</span></span>
                            <span class="time">⏱️ <span id="buy-time">15</span>s</span>
                        </div>
                        <button class="close-btn" id="close-buy-menu">✕</button>
                    </div>
                    
                    <div class="buy-menu-content">
                        <div class="category-tabs">
                            ${Array.from(this.categories.values()).map(cat => `
                                <button class="category-tab ${cat.id === 'rifles' ? 'active' : ''}" 
                                        data-category="${cat.id}">
                                    ${cat.icon} ${cat.name}
                                </button>
                            `).join('')}
                        </div>
                        
                        <div class="items-grid" id="items-grid">
                            <!-- Items serão inseridos aqui -->
                        </div>
                    </div>
                    
                    <div class="buy-menu-footer">
                        <div class="quick-buy">
                            <button class="quick-buy-btn" data-preset="eco">💰 Eco</button>
                            <button class="quick-buy-btn" data-preset="force">⚡ Force</button>
                            <button class="quick-buy-btn" data-preset="full">🔥 Full Buy</button>
                        </div>
                        <div class="inventory-preview">
                            <span>Inventário:</span>
                            <div id="inventory-items"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Adiciona estilos CSS
        this.addBuyMenuStyles();
        
        // Adiciona ao DOM
        document.body.appendChild(this.menuElement);
    }

    private addBuyMenuStyles(): void {
        const styleId = 'cs2-buy-menu-styles';
        if (document.getElementById(styleId)) return;

        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
            .cs2-buy-menu {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 1000;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }

            .cs2-buy-menu.hidden {
                display: none;
            }

            .buy-menu-overlay {
                background: rgba(0, 0, 0, 0.8);
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .buy-menu-container {
                background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
                border: 2px solid #444;
                border-radius: 10px;
                width: 90%;
                max-width: 1200px;
                height: 80%;
                display: flex;
                flex-direction: column;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            }

            .buy-menu-header {
                background: linear-gradient(90deg, #ff6b35 0%, #f7931e 100%);
                padding: 15px 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-radius: 8px 8px 0 0;
            }

            .buy-menu-header h2 {
                margin: 0;
                color: white;
                font-size: 24px;
                font-weight: bold;
            }

            .player-info {
                display: flex;
                gap: 20px;
                color: white;
                font-size: 18px;
                font-weight: bold;
            }

            .close-btn {
                background: rgba(255, 255, 255, 0.2);
                border: none;
                color: white;
                font-size: 20px;
                width: 30px;
                height: 30px;
                border-radius: 50%;
                cursor: pointer;
                transition: background 0.2s;
            }

            .close-btn:hover {
                background: rgba(255, 255, 255, 0.3);
            }

            .buy-menu-content {
                flex: 1;
                display: flex;
                flex-direction: column;
                padding: 20px;
            }

            .category-tabs {
                display: flex;
                gap: 10px;
                margin-bottom: 20px;
                flex-wrap: wrap;
            }

            .category-tab {
                background: #333;
                border: 2px solid #555;
                color: white;
                padding: 10px 15px;
                border-radius: 5px;
                cursor: pointer;
                transition: all 0.2s;
                font-size: 14px;
                font-weight: bold;
            }

            .category-tab:hover {
                background: #444;
                border-color: #666;
            }

            .category-tab.active {
                background: #ff6b35;
                border-color: #ff6b35;
            }

            .items-grid {
                flex: 1;
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 15px;
                overflow-y: auto;
                padding: 10px;
            }

            .weapon-item {
                background: #2a2a2a;
                border: 2px solid #444;
                border-radius: 8px;
                padding: 15px;
                cursor: pointer;
                transition: all 0.2s;
                position: relative;
            }

            .weapon-item:hover {
                border-color: #ff6b35;
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(255, 107, 53, 0.3);
            }

            .weapon-item.affordable {
                border-color: #4CAF50;
            }

            .weapon-item.expensive {
                border-color: #f44336;
                opacity: 0.6;
            }

            .weapon-name {
                color: white;
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 5px;
            }

            .weapon-price {
                color: #4CAF50;
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 10px;
            }

            .weapon-price.expensive {
                color: #f44336;
            }

            .weapon-stats {
                font-size: 12px;
                color: #ccc;
            }

            .buy-menu-footer {
                background: #222;
                padding: 15px 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-radius: 0 0 8px 8px;
            }

            .quick-buy {
                display: flex;
                gap: 10px;
            }

            .quick-buy-btn {
                background: #444;
                border: 2px solid #666;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                cursor: pointer;
                transition: all 0.2s;
                font-size: 14px;
            }

            .quick-buy-btn:hover {
                background: #555;
                border-color: #777;
            }

            .inventory-preview {
                color: white;
                font-size: 14px;
            }

            #inventory-items {
                display: flex;
                gap: 10px;
                margin-top: 5px;
            }

            .inventory-item {
                background: #333;
                padding: 5px 10px;
                border-radius: 3px;
                font-size: 12px;
            }
        `;

        document.head.appendChild(style);
    }

    private setupEventListeners(): void {
        if (!this.menuElement) return;

        // Fechar menu
        const closeBtn = this.menuElement.querySelector('#close-buy-menu');
        closeBtn?.addEventListener('click', () => this.closeBuyMenu());

        // Tabs de categoria
        const categoryTabs = this.menuElement.querySelectorAll('.category-tab');
        categoryTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                const target = e.target as HTMLElement;
                const category = target.dataset.category;
                if (category) {
                    this.selectCategory(category);
                }
            });
        });

        // Quick buy presets
        const quickBuyBtns = this.menuElement.querySelectorAll('.quick-buy-btn');
        quickBuyBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const target = e.target as HTMLElement;
                const preset = target.dataset.preset;
                if (preset) {
                    this.executeQuickBuy(preset);
                }
            });
        });

        // Fechar com ESC
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.menuState.isOpen) {
                this.closeBuyMenu();
            }
        });
    }

    public openBuyMenu(playerId: string): void {
        this.currentPlayerId = playerId;
        const playerEconomy = this.economySystem.getPlayerEconomy(playerId);
        
        if (!playerEconomy) {
            console.log('❌ Economia do jogador não encontrada');
            return;
        }

        this.menuState.isOpen = true;
        this.menuState.playerMoney = playerEconomy.money;
        this.menuState.team = playerEconomy.team;
        this.menuState.canBuy = true; // Seria verificado se está no freeze time

        this.updateMenuDisplay();
        this.menuElement?.classList.remove('hidden');
        
        console.log(`🛒 Buy menu aberto para ${playerId} ($${playerEconomy.money})`);
        this.emit('buyMenuOpened', { playerId, money: playerEconomy.money });
    }

    public closeBuyMenu(): void {
        this.menuState.isOpen = false;
        this.menuElement?.classList.add('hidden');
        
        if (this.currentPlayerId) {
            console.log(`🛒 Buy menu fechado para ${this.currentPlayerId}`);
            this.emit('buyMenuClosed', { playerId: this.currentPlayerId });
        }
    }

    private selectCategory(categoryId: string): void {
        this.menuState.selectedCategory = categoryId;
        
        // Atualiza tabs ativas
        const tabs = this.menuElement?.querySelectorAll('.category-tab');
        tabs?.forEach(tab => {
            tab.classList.toggle('active', tab.getAttribute('data-category') === categoryId);
        });

        this.updateItemsGrid();
    }

    private updateMenuDisplay(): void {
        if (!this.menuElement || !this.currentPlayerId) return;

        // Atualiza dinheiro
        const moneyElement = this.menuElement.querySelector('#player-money');
        if (moneyElement) {
            moneyElement.textContent = this.menuState.playerMoney.toString();
        }

        // Atualiza tempo (seria conectado ao sistema de rounds)
        const timeElement = this.menuElement.querySelector('#buy-time');
        if (timeElement) {
            timeElement.textContent = '15'; // Placeholder
        }

        this.updateItemsGrid();
        this.updateInventoryPreview();
    }

    private updateItemsGrid(): void {
        const itemsGrid = this.menuElement?.querySelector('#items-grid');
        if (!itemsGrid) return;

        const category = this.categories.get(this.menuState.selectedCategory);
        if (!category) return;

        // Filtra itens por time
        const availableItems = category.items.filter(item => 
            item.team === 'both' || item.team === this.menuState.team
        );

        itemsGrid.innerHTML = availableItems.map(item => {
            const canAfford = this.menuState.playerMoney >= item.price;
            const affordableClass = canAfford ? 'affordable' : 'expensive';
            
            return `
                <div class="weapon-item ${affordableClass}" data-item-id="${item.id}">
                    <div class="weapon-name">${item.name}</div>
                    <div class="weapon-price ${canAfford ? '' : 'expensive'}">$${item.price}</div>
                    <div class="weapon-stats">
                        Kill Reward: $${item.killReward}<br>
                        Categoria: ${item.category}
                    </div>
                </div>
            `;
        }).join('');

        // Adiciona listeners para compra
        const weaponItems = itemsGrid.querySelectorAll('.weapon-item');
        weaponItems.forEach(item => {
            item.addEventListener('click', (e) => {
                const target = e.currentTarget as HTMLElement;
                const itemId = target.dataset.itemId;
                if (itemId) {
                    this.buyItem(itemId);
                }
            });
        });
    }

    private updateInventoryPreview(): void {
        if (!this.currentPlayerId) return;

        const inventoryElement = this.menuElement?.querySelector('#inventory-items');
        if (!inventoryElement) return;

        const playerEconomy = this.economySystem.getPlayerEconomy(this.currentPlayerId);
        if (!playerEconomy) return;

        const inventoryItems = [];
        
        if (playerEconomy.inventory.primaryWeapon) {
            inventoryItems.push(playerEconomy.inventory.primaryWeapon);
        }
        if (playerEconomy.inventory.secondaryWeapon) {
            inventoryItems.push(playerEconomy.inventory.secondaryWeapon);
        }
        if (playerEconomy.inventory.armor) {
            inventoryItems.push(playerEconomy.inventory.helmet ? 'Kevlar+Helmet' : 'Kevlar');
        }
        if (playerEconomy.inventory.defuseKit) {
            inventoryItems.push('Defuse Kit');
        }
        inventoryItems.push(...playerEconomy.inventory.grenades);

        inventoryElement.innerHTML = inventoryItems.map(item => 
            `<span class="inventory-item">${item}</span>`
        ).join('');
    }

    private buyItem(itemId: string): void {
        if (!this.currentPlayerId) return;

        const success = this.economySystem.buyItem(this.currentPlayerId, itemId);
        
        if (success) {
            // Atualiza estado local
            const playerEconomy = this.economySystem.getPlayerEconomy(this.currentPlayerId);
            if (playerEconomy) {
                this.menuState.playerMoney = playerEconomy.money;
            }

            this.updateMenuDisplay();
            
            const weapon = this.economySystem.getWeaponPrice(itemId);
            console.log(`✅ Item comprado: ${weapon?.name} por $${weapon?.price}`);
            
            this.emit('itemPurchased', {
                playerId: this.currentPlayerId,
                itemId,
                weapon,
                remainingMoney: this.menuState.playerMoney
            });
        } else {
            console.log(`❌ Falha ao comprar item: ${itemId}`);
            this.emit('purchaseFailed', {
                playerId: this.currentPlayerId,
                itemId,
                reason: 'Dinheiro insuficiente ou item indisponível'
            });
        }
    }

    private executeQuickBuy(preset: string): void {
        if (!this.currentPlayerId) return;

        const presets = {
            eco: ['p250', 'kevlar'],
            force: ['ak47', 'kevlar', 'he_grenade'],
            full: ['ak47', 'kevlar_helmet', 'he_grenade', 'flashbang', 'smoke']
        };

        const items = presets[preset as keyof typeof presets] || [];
        
        items.forEach(itemId => {
            this.buyItem(itemId);
        });

        console.log(`⚡ Quick buy executado: ${preset}`);
        this.emit('quickBuyExecuted', {
            playerId: this.currentPlayerId,
            preset,
            items
        });
    }

    // Getters públicos
    public isOpen(): boolean {
        return this.menuState.isOpen;
    }

    public getMenuState(): BuyMenuState {
        return { ...this.menuState };
    }
}
