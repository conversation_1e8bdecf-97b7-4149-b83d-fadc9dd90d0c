// Copyright Tactical Nexus Team. All Rights Reserved.

#include "TacticalNexusUE5.h"
#include "Modules/ModuleManager.h"

DEFINE_LOG_CATEGORY(LogTacticalNexus);

void FTacticalNexusUE5Module::StartupModule()
{
	// This code will execute after your module is loaded into memory; the exact timing is specified in the .uplugin file per-module
	UE_LOG(LogTacticalNexus, Warning, TEXT("🎮 Tactical Nexus UE5 Module Started - Ready for CS2-style gameplay!"));
}

void FTacticalNexusUE5Module::ShutdownModule()
{
	// This function may be called during shutdown to clean up your module.  For modules that support dynamic reloading,
	// we call this function before unloading the module.
	UE_LOG(LogTacticalNexus, Warning, TEXT("🎮 Tactical Nexus UE5 Module Shutdown"));
}

IMPLEMENT_PRIMARY_GAME_MODULE(FTacticalNexusUE5Module, TacticalNexusUE5, "TacticalNexusUE5");
