import { EventEmitter } from 'events';
import { RoundSystem, RoundConfig } from './RoundSystem';
import { CS2EconomySystem } from './CS2EconomySystem';
import { Vector3 } from '../shared/types';

export interface GameModeConfig {
    id: string;
    name: string;
    description: string;
    maxPlayers: number;
    teamSize: number;
    roundConfig: RoundConfig;
    objectives: GameObjective[];
    spawnPoints: {
        terrorist: Vector3[];
        counterTerrorist: Vector3[];
    };
    buyZones: {
        terrorist: Vector3[];
        counterTerrorist: Vector3[];
    };
}

export interface GameObjective {
    id: string;
    type: 'bomb_site' | 'hostage_rescue' | 'elimination';
    position?: Vector3;
    radius?: number;
    description: string;
}

export interface Player {
    id: string;
    name: string;
    team: 'terrorist' | 'counter_terrorist';
    isAlive: boolean;
    position: Vector3;
    health: number;
    armor: number;
    money: number;
}

export interface MatchState {
    gameMode: string;
    currentRound: number;
    phase: string;
    timeRemaining: number;
    terroristScore: number;
    counterTerroristScore: number;
    players: Map<string, Player>;
    bombPlanted: boolean;
    bombSite?: 'A' | 'B';
    hostagesRescued: number;
}

export class CS2GameModes extends EventEmitter {
    private roundSystem: RoundSystem;
    private economySystem: CS2EconomySystem;
    private currentGameMode: GameModeConfig | null = null;
    private matchState: MatchState;
    private players: Map<string, Player> = new Map();

    // Configurações dos modos de jogo
    private gameModes: Map<string, GameModeConfig> = new Map();

    constructor() {
        super();
        this.initializeGameModes();
        this.matchState = this.createInitialMatchState();
        console.log('🎮 Sistema de Modos de Jogo CS2 inicializado');
    }

    private initializeGameModes(): void {
        // Competitive (Bomb Defusal)
        this.gameModes.set('competitive', {
            id: 'competitive',
            name: 'Competitive',
            description: 'Modo competitivo 5v5 com economia completa',
            maxPlayers: 10,
            teamSize: 5,
            roundConfig: {
                maxRounds: 24, // MR12
                roundTime: 115, // 1:55
                freezeTime: 15,
                warmupTime: 300,
                bombTimer: 40,
                defuseTime: 10,
                c4Timer: 40
            },
            objectives: [
                {
                    id: 'bomb_site_a',
                    type: 'bomb_site',
                    position: { x: 100, y: 0, z: 200 },
                    radius: 50,
                    description: 'Plant the bomb at site A'
                },
                {
                    id: 'bomb_site_b',
                    type: 'bomb_site',
                    position: { x: -150, y: 0, z: -100 },
                    radius: 50,
                    description: 'Plant the bomb at site B'
                },
                {
                    id: 'elimination',
                    type: 'elimination',
                    description: 'Eliminate all enemy players'
                }
            ],
            spawnPoints: {
                terrorist: [
                    { x: -200, y: 0, z: 0 },
                    { x: -180, y: 0, z: 20 },
                    { x: -220, y: 0, z: -20 },
                    { x: -200, y: 0, z: 40 },
                    { x: -240, y: 0, z: 0 }
                ],
                counterTerrorist: [
                    { x: 200, y: 0, z: 0 },
                    { x: 180, y: 0, z: 20 },
                    { x: 220, y: 0, z: -20 },
                    { x: 200, y: 0, z: 40 },
                    { x: 240, y: 0, z: 0 }
                ]
            },
            buyZones: {
                terrorist: [{ x: -200, y: 0, z: 0 }],
                counterTerrorist: [{ x: 200, y: 0, z: 0 }]
            }
        });

        // Casual (Bomb Defusal)
        this.gameModes.set('casual', {
            id: 'casual',
            name: 'Casual',
            description: 'Modo casual 10v10 com economia simplificada',
            maxPlayers: 20,
            teamSize: 10,
            roundConfig: {
                maxRounds: 16, // MR8
                roundTime: 120, // 2:00
                freezeTime: 20,
                warmupTime: 60,
                bombTimer: 45,
                defuseTime: 10,
                c4Timer: 45
            },
            objectives: [
                {
                    id: 'bomb_site_a',
                    type: 'bomb_site',
                    position: { x: 100, y: 0, z: 200 },
                    radius: 50,
                    description: 'Plant the bomb at site A'
                },
                {
                    id: 'bomb_site_b',
                    type: 'bomb_site',
                    position: { x: -150, y: 0, z: -100 },
                    radius: 50,
                    description: 'Plant the bomb at site B'
                }
            ],
            spawnPoints: {
                terrorist: [
                    { x: -200, y: 0, z: 0 }, { x: -180, y: 0, z: 20 }, { x: -220, y: 0, z: -20 },
                    { x: -200, y: 0, z: 40 }, { x: -240, y: 0, z: 0 }, { x: -160, y: 0, z: 0 },
                    { x: -200, y: 0, z: -40 }, { x: -180, y: 0, z: -20 }, { x: -220, y: 0, z: 20 },
                    { x: -260, y: 0, z: 0 }
                ],
                counterTerrorist: [
                    { x: 200, y: 0, z: 0 }, { x: 180, y: 0, z: 20 }, { x: 220, y: 0, z: -20 },
                    { x: 200, y: 0, z: 40 }, { x: 240, y: 0, z: 0 }, { x: 160, y: 0, z: 0 },
                    { x: 200, y: 0, z: -40 }, { x: 180, y: 0, z: -20 }, { x: 220, y: 0, z: 20 },
                    { x: 260, y: 0, z: 0 }
                ]
            },
            buyZones: {
                terrorist: [{ x: -200, y: 0, z: 0 }],
                counterTerrorist: [{ x: 200, y: 0, z: 0 }]
            }
        });

        // Deathmatch
        this.gameModes.set('deathmatch', {
            id: 'deathmatch',
            name: 'Deathmatch',
            description: 'Combate livre sem rounds ou economia',
            maxPlayers: 20,
            teamSize: 10,
            roundConfig: {
                maxRounds: 1,
                roundTime: 600, // 10 minutos
                freezeTime: 0,
                warmupTime: 10,
                bombTimer: 0,
                defuseTime: 0,
                c4Timer: 0
            },
            objectives: [
                {
                    id: 'elimination',
                    type: 'elimination',
                    description: 'Get the most kills'
                }
            ],
            spawnPoints: {
                terrorist: [
                    { x: -200, y: 0, z: 0 }, { x: -100, y: 0, z: 100 }, { x: 0, y: 0, z: 200 },
                    { x: 100, y: 0, z: 100 }, { x: 200, y: 0, z: 0 }, { x: 100, y: 0, z: -100 },
                    { x: 0, y: 0, z: -200 }, { x: -100, y: 0, z: -100 }, { x: -150, y: 0, z: 50 },
                    { x: 150, y: 0, z: -50 }
                ],
                counterTerrorist: [
                    { x: -200, y: 0, z: 0 }, { x: -100, y: 0, z: 100 }, { x: 0, y: 0, z: 200 },
                    { x: 100, y: 0, z: 100 }, { x: 200, y: 0, z: 0 }, { x: 100, y: 0, z: -100 },
                    { x: 0, y: 0, z: -200 }, { x: -100, y: 0, z: -100 }, { x: -150, y: 0, z: 50 },
                    { x: 150, y: 0, z: -50 }
                ]
            },
            buyZones: {
                terrorist: [],
                counterTerrorist: []
            }
        });

        console.log(`🎮 ${this.gameModes.size} modos de jogo carregados`);
    }

    public startMatch(gameModeId: string, playerIds: string[]): boolean {
        const gameMode = this.gameModes.get(gameModeId);
        if (!gameMode) {
            console.log(`❌ Modo de jogo não encontrado: ${gameModeId}`);
            return false;
        }

        if (playerIds.length > gameMode.maxPlayers) {
            console.log(`❌ Muitos jogadores para o modo ${gameMode.name}: ${playerIds.length}/${gameMode.maxPlayers}`);
            return false;
        }

        this.currentGameMode = gameMode;
        
        // Inicializa sistemas
        this.roundSystem = new RoundSystem(gameMode.roundConfig);
        this.economySystem = new CS2EconomySystem(this.roundSystem);
        
        // Distribui jogadores em times
        this.assignPlayersToTeams(playerIds);
        
        // Configura listeners
        this.setupGameModeListeners();

        console.log(`🚀 Partida iniciada - Modo: ${gameMode.name}, Jogadores: ${playerIds.length}`);
        this.emit('matchStarted', {
            gameMode: gameMode.id,
            players: Array.from(this.players.values()),
            objectives: gameMode.objectives
        });

        return true;
    }

    private assignPlayersToTeams(playerIds: string[]): void {
        this.players.clear();
        
        playerIds.forEach((playerId, index) => {
            const team = index % 2 === 0 ? 'terrorist' : 'counter_terrorist';
            const spawnPoints = this.currentGameMode!.spawnPoints[team];
            const spawnIndex = Math.floor(index / 2) % spawnPoints.length;
            
            const player: Player = {
                id: playerId,
                name: `Player_${playerId}`,
                team,
                isAlive: true,
                position: spawnPoints[spawnIndex],
                health: 100,
                armor: 0,
                money: 800
            };

            this.players.set(playerId, player);
            
            // Cria economia para o jogador
            this.economySystem.createPlayerEconomy(playerId, team);
        });

        console.log(`👥 Jogadores distribuídos - T: ${this.getTeamPlayerCount('terrorist')}, CT: ${this.getTeamPlayerCount('counter_terrorist')}`);
    }

    private setupGameModeListeners(): void {
        this.roundSystem.on('roundStarted', (data) => {
            this.updateMatchState();
            this.spawnPlayers();
            this.emit('roundStarted', data);
        });

        this.roundSystem.on('roundEnded', (data) => {
            this.updateMatchState();
            this.emit('roundEnded', data);
        });

        this.roundSystem.on('bombPlanted', (data) => {
            this.matchState.bombPlanted = true;
            this.matchState.bombSite = data.site;
            this.emit('bombPlanted', data);
        });

        this.economySystem.on('itemPurchased', (data) => {
            this.emit('itemPurchased', data);
        });
    }

    private spawnPlayers(): void {
        if (!this.currentGameMode) return;

        this.players.forEach((player) => {
            if (player.isAlive) {
                const spawnPoints = this.currentGameMode!.spawnPoints[player.team];
                const randomSpawn = spawnPoints[Math.floor(Math.random() * spawnPoints.length)];
                
                player.position = randomSpawn;
                player.health = 100;
                player.armor = 0; // Reset armor (seria baseado no inventário)
            }
        });

        console.log('🎯 Jogadores spawnados');
        this.emit('playersSpawned', Array.from(this.players.values()));
    }

    private updateMatchState(): void {
        if (!this.roundSystem) return;

        this.matchState = {
            gameMode: this.currentGameMode?.id || '',
            currentRound: this.roundSystem.getCurrentRound(),
            phase: this.roundSystem.getCurrentPhase(),
            timeRemaining: 0, // Seria calculado baseado no tempo restante
            terroristScore: this.roundSystem.getTeamScore().terroristRounds,
            counterTerroristScore: this.roundSystem.getTeamScore().counterTerroristRounds,
            players: new Map(this.players),
            bombPlanted: this.roundSystem.isBombPlanted(),
            bombSite: this.roundSystem.getBombSite() || undefined,
            hostagesRescued: 0
        };
    }

    private createInitialMatchState(): MatchState {
        return {
            gameMode: '',
            currentRound: 0,
            phase: 'warmup',
            timeRemaining: 0,
            terroristScore: 0,
            counterTerroristScore: 0,
            players: new Map(),
            bombPlanted: false,
            hostagesRescued: 0
        };
    }

    public eliminatePlayer(playerId: string, killerId?: string): void {
        const player = this.players.get(playerId);
        if (!player) return;

        player.isAlive = false;
        player.health = 0;

        if (this.roundSystem) {
            this.roundSystem.eliminatePlayer(playerId, killerId);
        }

        console.log(`💀 ${playerId} foi eliminado${killerId ? ` por ${killerId}` : ''}`);
        this.emit('playerEliminated', { playerId, killerId });

        // Verifica se o round acabou
        this.checkRoundEndConditions();
    }

    private checkRoundEndConditions(): void {
        const aliveTerrorists = this.getAlivePlayerCount('terrorist');
        const aliveCounterTerrorists = this.getAlivePlayerCount('counter_terrorist');

        if (aliveTerrorists === 0) {
            this.roundSystem?.endRound('counter_terrorist', 'elimination');
        } else if (aliveCounterTerrorists === 0) {
            this.roundSystem?.endRound('terrorist', 'elimination');
        }
    }

    private getTeamPlayerCount(team: 'terrorist' | 'counter_terrorist'): number {
        return Array.from(this.players.values()).filter(p => p.team === team).length;
    }

    private getAlivePlayerCount(team: 'terrorist' | 'counter_terrorist'): number {
        return Array.from(this.players.values()).filter(p => p.team === team && p.isAlive).length;
    }

    // Getters públicos
    public getCurrentGameMode(): GameModeConfig | null {
        return this.currentGameMode;
    }

    public getMatchState(): MatchState {
        return { ...this.matchState };
    }

    public getPlayer(playerId: string): Player | undefined {
        return this.players.get(playerId);
    }

    public getAllPlayers(): Player[] {
        return Array.from(this.players.values());
    }

    public getGameModes(): GameModeConfig[] {
        return Array.from(this.gameModes.values());
    }

    public getRoundSystem(): RoundSystem | undefined {
        return this.roundSystem;
    }

    public getEconomySystem(): CS2EconomySystem | undefined {
        return this.economySystem;
    }
}
