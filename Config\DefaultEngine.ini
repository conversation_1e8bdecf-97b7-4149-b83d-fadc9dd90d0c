[/Script/EngineSettings.GameMapsSettings]
GameDefaultMap=/Game/Maps/MainMenu
EditorStartupMap=/Game/Maps/Dust2
GlobalDefaultGameMode=/Script/TacticalNexusUE5.TacticalNexusGameMode
GlobalDefaultServerGameMode=/Script/TacticalNexusUE5.TacticalNexusGameMode

[/Script/Engine.Engine]
+ActiveGameNameRedirects=(OldGameName="TP_FirstPersonBP",NewGameName="/Script/TacticalNexusUE5")
+ActiveGameNameRedirects=(OldGameName="/Script/TP_FirstPersonBP",NewGameName="/Script/TacticalNexusUE5")

[/Script/HardwareTargeting.HardwareTargetingSettings]
TargetedHardwareClass=Desktop
AppliedTargetedHardwareClass=Desktop
DefaultGraphicsPerformance=Maximum
AppliedDefaultGraphicsPerformance=Maximum

[/Script/Engine.RendererSettings]
r.GenerateMeshDistanceFields=True
r.DynamicGlobalIlluminationMethod=1
r.ReflectionMethod=1
r.Shadow.Virtual.Enable=1
r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange=True
r.DefaultFeature.LocalExposure.HighlightContrastScale=0.8
r.DefaultFeature.LocalExposure.ShadowContrastScale=0.8
r.AntiAliasing=3
r.MSAACount=8
r.DefaultFeature.MotionBlur=0
r.MotionBlurQuality=0
r.SceneColorFormat=4
r.AllowStaticLighting=False
r.NormalMapsForStaticLighting=False
r.ForwardShading=False
r.VertexFoggingForOpaque=True
r.SeparateTranslucency=True
r.TranslucentLightingVolume=True
r.TranslucentSortPolicy=0
r.CustomDepth=1
r.CustomDepthTemporalAAJitter=True
r.PostProcessAAQuality=4
r.BloomQuality=5
r.FastBlurThreshold=100
r.Upsampling=1
r.Tonemapper.GrainQuantization=1
r.LightShaftDownSampleFactor=2
r.LightShaftQuality=1
r.LightShaftBlurPasses=6
r.AmbientOcclusionMaxQuality=100
r.AmbientOcclusionLevels=-1
r.AmbientOcclusionRadiusScale=1.0
r.DepthOfFieldQuality=2
r.RenderTargetPoolMin=400
r.LensFlareQuality=2
r.SceneColorFringeQuality=1
r.EyeAdaptationQuality=2
r.BloomQuality=5
r.FastBlurThreshold=100
r.Upsampling=1
r.Tonemapper.GrainQuantization=1
r.LightShaftDownSampleFactor=2
r.LightShaftQuality=1
r.LightShaftBlurPasses=6
r.AmbientOcclusionMaxQuality=100
r.AmbientOcclusionLevels=-1
r.AmbientOcclusionRadiusScale=1.0
r.DepthOfFieldQuality=2
r.RenderTargetPoolMin=400
r.LensFlareQuality=2
r.SceneColorFringeQuality=1
r.EyeAdaptationQuality=2

; Lumen settings for performance
r.Lumen.GlobalIllumination.MaxLumenMeshCards=2048
r.Lumen.Reflections.MaxLumenMeshCards=1024
r.Lumen.GlobalIllumination.CardUpdateFactor=4
r.Lumen.Reflections.CardUpdateFactor=4

; Nanite settings
r.Nanite.MaxPixelsPerEdge=1.0
r.Nanite.MinPixelsPerEdgeHW=64

; Performance optimizations for competitive gaming
r.Streaming.PoolSize=3000
r.Streaming.MaxNumTextures=2048
r.Streaming.Boost=1
r.MaxAnisotropy=16
r.ViewDistanceScale=1.0
r.SkeletalMeshLODBias=0
r.StaticMeshLODBias=0

[/Script/WorldPartition.WorldPartitionSubsystem]
EnableStreaming=True

[/Script/Engine.NetworkSettings]
n.VerifyPeer=False

[/Script/OnlineSubsystemSteam.SteamNetDriver]
NetConnectionClassName="OnlineSubsystemSteam.SteamNetConnection"

[OnlineSubsystem]
DefaultPlatformService=Steam

[OnlineSubsystemSteam]
bEnabled=true
SteamDevAppId=480
bInitServerOnClient=true
bClientOptimizeForListen=true
bAllowP2PPacketRelay=true
P2PConnectionTimeout=90.0

[/Script/Engine.GameEngine]
+NetDriverDefinitions=(DefName="GameNetDriver",DriverClassName="OnlineSubsystemSteam.SteamNetDriver",DriverClassNameFallback="OnlineSubsystemUtils.IpNetDriver")

; Network settings for competitive gaming
[/Script/OnlineSubsystemUtils.IpNetDriver]
LanServerMaxTickRate=128
NetServerMaxTickRate=128
MaxNetTickRate=128
MaxInternetClientRate=25000
MaxClientRate=25000
InitialConnectTimeout=200.0
ConnectionTimeout=120.0
TimeoutMultiplierForUnoptimizedBuilds=4.0

; Replication settings
[/Script/Engine.ReplicationDriver]
ReplicationDriverClassName="ReplicationGraph.BasicReplicationGraph"

; Audio settings
[/Script/Engine.AudioSettings]
DefaultSoundClassName=/Script/Engine.SoundClass
DefaultMediaSoundClassName=/Script/Engine.SoundClass
DefaultSoundConcurrencyName=/Script/Engine.SoundConcurrency
DefaultBaseSoundMix=/Script/Engine.SoundMix
VoiPSoundClass=/Script/Engine.SoundClass
MasterSubmixName=/Script/Engine.SoundSubmix
BaseDefaultSubmixName=/Script/Engine.SoundSubmix
ReverbSubmixName=/Script/Engine.SoundSubmix
EQSubmixName=/Script/Engine.SoundSubmix
AudioMixerPlatformSettings=()
SpatializationPlugin=
SourceDataOverridePlugin=
ReverbPlugin=SteamAudio
OcclusionPlugin=SteamAudio
CompressionOverrides=(bOverrideCompressionTimes=False,DurationThreshold=5.000000,MaxNumRandomBranches=0,SoundCueQualityIndex=0)
CacheSizeKB=65536
MaxChunkSizeOverrideKB=0
bResampleForDevice=False
MaxSampleRate=48000.000000
HighSampleRate=32000.000000
MedSampleRate=22050.000000
LowSampleRate=11025.000000
MinSampleRate=8000.000000
CompressionQualityModifier=1.000000
AutoStreamingThreshold=0.000000
SoundCueCookQualityIndex=-1

; Input settings for competitive gaming
[/Script/Engine.InputSettings]
bAltEnterTogglesFullscreen=True
bF11TogglesFullscreen=True
bUseMouseForTouch=False
bEnableMouseSmoothing=False
bEnableFOVScaling=True
bCaptureMouseOnLaunch=True
bAlwaysShowTouchInterface=False
bShowConsoleOnFourFingerTap=True
bEnableGestureRecognizer=False
bUseAutocorrect=False
DefaultViewportMouseCaptureMode=CapturePermanently_IncludingInitialMouseDown
DefaultViewportMouseLockMode=LockOnCapture
FOVScale=0.011110
DoubleClickTime=0.200000

; Console variables for performance
[ConsoleVariables]
r.VSync=0
r.FinishCurrentFrame=0
t.MaxFPS=999
r.OneFrameThreadLag=0
r.RHICmdBypass=0
r.ShowMaterialDrawEvents=0

; Garbage collection settings
gc.MaxObjectsNotConsideredByGC=1
gc.SizeOfPermanentObjectPool=0
gc.FlushStreamingOnGC=1
gc.NumRetriesBeforeForcingGC=10
gc.AllowParallelGC=1
gc.TimeBetweenPurgingPendingKillObjects=61
gc.MaxObjectsInEditor=25165824
gc.IncrementalBeginDestroyEnabled=1
gc.CreateGCClusters=1
gc.MinGCClusterSize=5
gc.ActorClusteringEnabled=1
gc.BlueprintClusteringEnabled=0
gc.UseDisregardForGCOnDedicatedServers=0

; Memory settings
[Memory]
MemoryStatCheckFrequency=1.0
bLogMemoryStatExecutionTime=0
bLogMemoryStatSummary=1
