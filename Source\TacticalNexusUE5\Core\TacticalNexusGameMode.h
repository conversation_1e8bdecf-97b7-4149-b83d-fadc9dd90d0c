// Copyright Tactical Nexus Team. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameModeBase.h"
#include "Engine/DataTable.h"
#include "TacticalNexusGameMode.generated.h"

UENUM(BlueprintType)
enum class EGamePhase : uint8
{
    WarmUp      UMETA(DisplayName = "Warm Up"),
    FreezeTime  UMETA(DisplayName = "Freeze Time"),
    RoundActive UMETA(DisplayName = "Round Active"),
    PostRound   UMETA(DisplayName = "Post Round"),
    HalfTime    UMETA(DisplayName = "Half Time"),
    MatchEnd    UMETA(DisplayName = "Match End")
};

UENUM(BlueprintType)
enum class ETeam : uint8
{
    None            UMETA(DisplayName = "None"),
    Terrorist       UMETA(DisplayName = "Terrorist"),
    CounterTerrorist UMETA(DisplayName = "Counter-Terrorist"),
    Spectator       UMETA(DisplayName = "Spectator")
};

UENUM(BlueprintType)
enum class ERoundEndReason : uint8
{
    None                UMETA(DisplayName = "None"),
    TerroristsEliminated UMETA(DisplayName = "Terrorists Eliminated"),
    CounterTerroristsEliminated UMETA(DisplayName = "Counter-Terrorists Eliminated"),
    BombExploded        UMETA(DisplayName = "Bomb Exploded"),
    BombDefused         UMETA(DisplayName = "Bomb Defused"),
    TimeExpired         UMETA(DisplayName = "Time Expired"),
    RoundDraw           UMETA(DisplayName = "Round Draw")
};

USTRUCT(BlueprintType)
struct FMatchConfig : public FTableRowBase
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Config")
    int32 MaxRounds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Config")
    float RoundTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Config")
    float FreezeTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Config")
    float BuyTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Config")
    float WarmUpTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Config")
    int32 MaxPlayers;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Config")
    bool bEnableOvertimeRounds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Config")
    int32 OvertimeMaxRounds;

    FMatchConfig()
    {
        MaxRounds = 24; // MR12 format (first to 13)
        RoundTime = 115.0f; // 1:55 minutes
        FreezeTime = 15.0f;
        BuyTime = 20.0f;
        WarmUpTime = 300.0f; // 5 minutes
        MaxPlayers = 10;
        bEnableOvertimeRounds = true;
        OvertimeMaxRounds = 6; // MR3 overtime
    }
};

USTRUCT(BlueprintType)
struct FTeamScore
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Score")
    int32 RoundsWon;

    UPROPERTY(BlueprintReadOnly, Category = "Score")
    int32 ConsecutiveRounds;

    UPROPERTY(BlueprintReadOnly, Category = "Score")
    int32 EconomyMoney;

    FTeamScore()
    {
        RoundsWon = 0;
        ConsecutiveRounds = 0;
        EconomyMoney = 800; // Starting money
    }
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnGamePhaseChanged, EGamePhase, NewPhase);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnRoundEnd, ERoundEndReason, Reason, ETeam, WinningTeam, int32, RoundNumber);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnMatchEnd, ETeam, WinningTeam, FTeamScore, FinalScore);

UCLASS(Blueprintable)
class TACTICALNEXUSUE5_API ATacticalNexusGameMode : public AGameModeBase
{
    GENERATED_BODY()

public:
    ATacticalNexusGameMode();

protected:
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;

public:
    // Match Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Match Config")
    FMatchConfig MatchConfig;

    // Current Match State
    UPROPERTY(BlueprintReadOnly, Category = "Match State")
    EGamePhase CurrentPhase;

    UPROPERTY(BlueprintReadOnly, Category = "Match State")
    int32 CurrentRound;

    UPROPERTY(BlueprintReadOnly, Category = "Match State")
    float RemainingTime;

    UPROPERTY(BlueprintReadOnly, Category = "Match State")
    bool bIsOvertime;

    UPROPERTY(BlueprintReadOnly, Category = "Match State")
    bool bSidesSwapped;

    // Team Scores
    UPROPERTY(BlueprintReadOnly, Category = "Score")
    FTeamScore TerroristScore;

    UPROPERTY(BlueprintReadOnly, Category = "Score")
    FTeamScore CounterTerroristScore;

    // Events
    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnGamePhaseChanged OnGamePhaseChanged;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnRoundEnd OnRoundEnd;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnMatchEnd OnMatchEnd;

    // Game Flow Functions
    UFUNCTION(BlueprintCallable, Category = "Game Flow")
    void StartWarmUp();

    UFUNCTION(BlueprintCallable, Category = "Game Flow")
    void StartMatch();

    UFUNCTION(BlueprintCallable, Category = "Game Flow")
    void StartRound();

    UFUNCTION(BlueprintCallable, Category = "Game Flow")
    void EndRound(ERoundEndReason Reason, ETeam WinningTeam);

    UFUNCTION(BlueprintCallable, Category = "Game Flow")
    void EndMatch(ETeam WinningTeam);

    UFUNCTION(BlueprintCallable, Category = "Game Flow")
    void SwapSides();

    // Player Management
    UFUNCTION(BlueprintCallable, Category = "Player Management")
    void AssignPlayerToTeam(APlayerController* PlayerController, ETeam Team);

    UFUNCTION(BlueprintCallable, Category = "Player Management")
    int32 GetTeamPlayerCount(ETeam Team) const;

    UFUNCTION(BlueprintCallable, Category = "Player Management")
    TArray<APlayerController*> GetTeamPlayers(ETeam Team) const;

    // Economy System
    UFUNCTION(BlueprintCallable, Category = "Economy")
    void AwardTeamMoney(ETeam Team, int32 Amount);

    UFUNCTION(BlueprintCallable, Category = "Economy")
    void ProcessRoundEconomy(ERoundEndReason Reason, ETeam WinningTeam);

    UFUNCTION(BlueprintCallable, Category = "Economy")
    int32 CalculateRoundLossBonusMoney(ETeam Team) const;

    // Bomb System
    UFUNCTION(BlueprintCallable, Category = "Bomb")
    void OnBombPlanted(FVector Location, APlayerController* Planter);

    UFUNCTION(BlueprintCallable, Category = "Bomb")
    void OnBombDefused(APlayerController* Defuser);

    UFUNCTION(BlueprintCallable, Category = "Bomb")
    void OnBombExploded();

    // Utility Functions
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Utility")
    bool IsMatchActive() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Utility")
    bool IsRoundActive() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Utility")
    bool IsBuyTimeActive() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Utility")
    ETeam GetWinningTeam() const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Utility")
    int32 GetRoundsToWin() const;

protected:
    // Internal Functions
    void ChangeGamePhase(EGamePhase NewPhase);
    void UpdateTimer(float DeltaTime);
    void CheckRoundEndConditions();
    void CheckMatchEndConditions();
    void RespawnAllPlayers();
    void ResetPlayerStates();
    void InitializeRoundEconomy();

    // Timer Handles
    FTimerHandle PhaseTimerHandle;
    FTimerHandle RoundTimerHandle;

    // Player tracking
    TMap<ETeam, TArray<APlayerController*>> TeamPlayers;
    TArray<APlayerController*> SpectatorPlayers;

    // Round statistics
    int32 TerroristConsecutiveLosses;
    int32 CounterTerroristConsecutiveLosses;

private:
    // Internal state tracking
    bool bMatchStarted;
    bool bRoundInProgress;
    float PhaseStartTime;
    bool bBombPlanted;
    FVector BombLocation;
    APlayerController* BombPlanter;
};
