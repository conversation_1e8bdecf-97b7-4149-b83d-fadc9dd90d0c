// Copyright Tactical Nexus Team. All Rights Reserved.

#include "M4A4Weapon.h"
#include "Engine/Engine.h"
#include "Kismet/GameplayStatics.h"
#include "../Characters/TacticalNexusCharacter.h"
#include "../TacticalNexusUE5.h"

AM4A4Weapon::AM4A4Weapon()
{
    // Initialize M4A4 specific properties
    HeadshotMultiplier = 4.0f; // 132 damage headshot (33 * 4)
    ArmorPenetration = 0.70f; // 70% armor penetration
    MovementInaccuracyMultiplier = 2.5f; // Less movement penalty than AK-47
    CrouchAccuracyBonus = 0.6f; // Better crouching bonus
    FirstShotAccuracy = 0.97f; // Better first shot accuracy than AK-47
    RecoilControlBonus = 0.8f; // Easier to control recoil
    
    bFirstShot = true;
    LastFireTime = 0.0f;
    ConsecutiveShots = 0;

    // Initialize M4A4 stats
    InitializeM4A4Stats();
    SetupM4A4RecoilPattern();

    UE_LOG(LogTacticalNexus, Log, TEXT("🔫 M4A4 Weapon Initialized - CS2 Style"));
}

void AM4A4Weapon::BeginPlay()
{
    Super::BeginPlay();
    
    // Override base weapon sounds with M4A4 specific sounds
    if (M4A4FireSound)
    {
        FireSound = M4A4FireSound;
    }
    
    if (M4A4ReloadSound)
    {
        ReloadSound = M4A4ReloadSound;
    }
    
    if (M4A4MuzzleFlash)
    {
        MuzzleFlashVFX = M4A4MuzzleFlash;
    }
}

void AM4A4Weapon::InitializeM4A4Stats()
{
    // CS2 accurate M4A4 stats
    WeaponStats.WeaponName = TEXT("M4A4");
    WeaponStats.WeaponType = EWeaponType::Rifle;
    WeaponStats.Damage = 33; // Base damage
    WeaponStats.FireRate = 666.0f; // 666 RPM (faster than AK-47)
    WeaponStats.Range = 8192.0f; // Long range
    WeaponStats.Accuracy = 0.78f; // Better base accuracy than AK-47
    WeaponStats.MagazineSize = 30;
    WeaponStats.ReloadTime = 3.1f; // Slightly slower reload than AK-47
    WeaponStats.Price = 3100; // CS2 price
    WeaponStats.bIsAutomatic = true;

    // Update derived values
    TimeBetweenShots = 60.0f / WeaponStats.FireRate; // ~0.09 seconds between shots
    CurrentAmmo = WeaponStats.MagazineSize;
    ReserveAmmo = 90; // 3 extra magazines
}

void AM4A4Weapon::SetupM4A4RecoilPattern()
{
    // CS2 accurate M4A4 recoil pattern (30 shots)
    // M4A4 has more predictable and controllable recoil than AK-47
    M4A4RecoilPattern.Empty();
    
    // First 8 shots - vertical climb (more controlled than AK-47)
    M4A4RecoilPattern.Add(FVector2D(0.0f, 15.0f));    // Shot 1
    M4A4RecoilPattern.Add(FVector2D(-1.0f, 28.0f));   // Shot 2
    M4A4RecoilPattern.Add(FVector2D(1.0f, 36.0f));    // Shot 3
    M4A4RecoilPattern.Add(FVector2D(-1.0f, 42.0f));   // Shot 4
    M4A4RecoilPattern.Add(FVector2D(2.0f, 46.0f));    // Shot 5
    M4A4RecoilPattern.Add(FVector2D(-2.0f, 49.0f));   // Shot 6
    M4A4RecoilPattern.Add(FVector2D(1.0f, 51.0f));    // Shot 7
    M4A4RecoilPattern.Add(FVector2D(-2.0f, 52.0f));   // Shot 8
    
    // Shots 9-18 - left side spray (more controlled)
    M4A4RecoilPattern.Add(FVector2D(-4.0f, 53.0f));   // Shot 9
    M4A4RecoilPattern.Add(FVector2D(-6.0f, 52.0f));   // Shot 10
    M4A4RecoilPattern.Add(FVector2D(-8.0f, 51.0f));   // Shot 11
    M4A4RecoilPattern.Add(FVector2D(-10.0f, 50.0f));  // Shot 12
    M4A4RecoilPattern.Add(FVector2D(-12.0f, 49.0f));  // Shot 13
    M4A4RecoilPattern.Add(FVector2D(-14.0f, 48.0f));  // Shot 14
    M4A4RecoilPattern.Add(FVector2D(-15.0f, 47.0f));  // Shot 15
    M4A4RecoilPattern.Add(FVector2D(-16.0f, 46.0f));  // Shot 16
    M4A4RecoilPattern.Add(FVector2D(-15.0f, 45.0f));  // Shot 17
    M4A4RecoilPattern.Add(FVector2D(-14.0f, 44.0f));  // Shot 18
    
    // Shots 19-30 - right side spray (smooth transition)
    M4A4RecoilPattern.Add(FVector2D(-12.0f, 43.0f));  // Shot 19
    M4A4RecoilPattern.Add(FVector2D(-8.0f, 42.0f));   // Shot 20
    M4A4RecoilPattern.Add(FVector2D(-4.0f, 41.0f));   // Shot 21
    M4A4RecoilPattern.Add(FVector2D(0.0f, 40.0f));    // Shot 22
    M4A4RecoilPattern.Add(FVector2D(4.0f, 39.0f));    // Shot 23
    M4A4RecoilPattern.Add(FVector2D(8.0f, 38.0f));    // Shot 24
    M4A4RecoilPattern.Add(FVector2D(12.0f, 37.0f));   // Shot 25
    M4A4RecoilPattern.Add(FVector2D(14.0f, 36.0f));   // Shot 26
    M4A4RecoilPattern.Add(FVector2D(16.0f, 35.0f));   // Shot 27
    M4A4RecoilPattern.Add(FVector2D(15.0f, 34.0f));   // Shot 28
    M4A4RecoilPattern.Add(FVector2D(14.0f, 33.0f));   // Shot 29
    M4A4RecoilPattern.Add(FVector2D(12.0f, 32.0f));   // Shot 30
    
    // Set the recoil pattern in the base weapon
    RecoilPattern.RecoilPoints = M4A4RecoilPattern;
    RecoilPattern.RecoilMultiplier = RecoilControlBonus; // Easier to control
    RecoilPattern.RecoveryRate = 10.0f; // Faster recovery than AK-47
}

void AM4A4Weapon::StartFire()
{
    if (!CanFire())
    {
        return;
    }

    // Reset recoil if enough time has passed (first shot accuracy)
    float CurrentTime = GetWorld()->GetTimeSeconds();
    if (CurrentTime - LastFireTime > 0.30f) // M4A4 first shot reset time (faster than AK-47)
    {
        bFirstShot = true;
        CurrentRecoilIndex = 0;
        CurrentRecoilOffset = FVector2D::ZeroVector;
        ConsecutiveShots = 0;
    }

    Super::StartFire();
}

void AM4A4Weapon::Fire()
{
    if (!CanFire())
    {
        return;
    }

    // Update firing state
    LastFireTime = GetWorld()->GetTimeSeconds();
    ConsecutiveShots++;
    
    // Consume ammo
    CurrentAmmo--;

    // Perform line trace with M4A4 specific calculations
    FVector StartLocation;
    FVector EndLocation;
    FRotator FireRotation;

    if (OwnerCharacter)
    {
        FVector CameraLocation;
        FRotator CameraRotation;
        OwnerCharacter->GetActorEyesViewPoint(CameraLocation, CameraRotation);

        // Calculate M4A4 specific spread and recoil
        FVector RecoilOffset = CalculateRecoil();
        FVector SpreadOffset = CalculateM4A4Spread();
        
        FireRotation = CameraRotation + FRotator(
            RecoilOffset.Y + SpreadOffset.Y,
            RecoilOffset.X + SpreadOffset.X,
            0.0f
        );

        StartLocation = CameraLocation;
        EndLocation = StartLocation + (FireRotation.Vector() * WeaponStats.Range);
    }

    // Perform line trace
    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.AddIgnoredActor(this);
    QueryParams.AddIgnoredActor(GetOwner());
    QueryParams.bTraceComplex = true;

    bool bHit = GetWorld()->LineTraceSingleByChannel(
        HitResult,
        StartLocation,
        EndLocation,
        ECC_Visibility,
        QueryParams
    );

    if (bHit)
    {
        ProcessHit(HitResult);
    }

    // Play effects
    MulticastPlayFireEffects();

    // Update recoil pattern
    if (CurrentRecoilIndex < M4A4RecoilPattern.Num())
    {
        FVector2D RecoilVector = M4A4RecoilPattern[CurrentRecoilIndex];
        
        // Apply first shot accuracy bonus
        if (bFirstShot)
        {
            RecoilVector *= 0.2f; // Better first shot than AK-47
            bFirstShot = false;
        }
        
        CurrentRecoilOffset += RecoilVector * RecoilPattern.RecoilMultiplier;
        CurrentRecoilIndex++;
    }

    UE_LOG(LogTacticalNexus, VeryVerbose, TEXT("🔫 M4A4 Fired - Shot %d, Recoil: %s"), 
        ConsecutiveShots, *CurrentRecoilOffset.ToString());
}

void AM4A4Weapon::ProcessHit(const FHitResult& HitResult)
{
    if (!HitResult.GetActor())
    {
        return;
    }

    // Calculate M4A4 specific damage
    float DamageAmount = CalculateM4A4Damage(HitResult);
    
    UE_LOG(LogTacticalNexus, Log, TEXT("🎯 M4A4 Hit - Damage: %.1f, Location: %s"), 
        DamageAmount, *HitResult.BoneName.ToString());

    // Apply damage
    UGameplayStatics::ApplyPointDamage(
        HitResult.GetActor(),
        DamageAmount,
        HitResult.Location,
        HitResult,
        OwnerCharacter ? OwnerCharacter->GetController() : nullptr,
        this,
        UDamageType::StaticClass()
    );

    // Spawn impact effects
    if (ImpactVFX)
    {
        UGameplayStatics::SpawnEmitterAtLocation(
            GetWorld(),
            ImpactVFX,
            HitResult.Location,
            HitResult.Normal.Rotation()
        );
    }
}

FVector AM4A4Weapon::CalculateM4A4Spread()
{
    float SpreadAmount = (1.0f - WeaponStats.Accuracy);
    
    // Apply first shot accuracy
    if (bFirstShot)
    {
        SpreadAmount *= (1.0f - FirstShotAccuracy);
    }
    
    // Modify spread based on player state
    if (OwnerCharacter)
    {
        // Increase spread when moving (less penalty than AK-47)
        float VelocitySize = OwnerCharacter->GetVelocity().Size();
        if (VelocitySize > 10.0f)
        {
            float MovementFactor = FMath::Clamp(VelocitySize / 250.0f, 0.0f, 1.0f);
            SpreadAmount *= (1.0f + MovementFactor * MovementInaccuracyMultiplier);
        }
        
        // Decrease spread when crouching (better bonus than AK-47)
        if (OwnerCharacter->bIsCrouched)
        {
            SpreadAmount *= CrouchAccuracyBonus;
        }
        
        // Increase spread with consecutive shots (less penalty than AK-47)
        float ConsecutiveShotsPenalty = FMath::Min(ConsecutiveShots * 0.08f, 1.5f);
        SpreadAmount *= (1.0f + ConsecutiveShotsPenalty);
    }

    return FVector(
        FMath::RandRange(-SpreadAmount, SpreadAmount),
        FMath::RandRange(-SpreadAmount, SpreadAmount),
        0.0f
    );
}

float AM4A4Weapon::CalculateM4A4Damage(const FHitResult& HitResult)
{
    float BaseDamage = WeaponStats.Damage;
    
    // Check for headshot
    bool bIsHeadshot = (HitResult.BoneName == TEXT("head") || 
                       HitResult.BoneName == TEXT("Head") ||
                       HitResult.BoneName.ToString().Contains(TEXT("head")));
    
    if (bIsHeadshot)
    {
        BaseDamage *= HeadshotMultiplier; // 132 damage headshot
        UE_LOG(LogTacticalNexus, Warning, TEXT("💀 M4A4 HEADSHOT! Damage: %.1f"), BaseDamage);
    }
    
    // Apply armor penetration if target has armor
    ATacticalNexusCharacter* TargetCharacter = Cast<ATacticalNexusCharacter>(HitResult.GetActor());
    if (TargetCharacter && TargetCharacter->Armor > 0.0f)
    {
        // CS2 armor calculation
        float ArmorDamageReduction = (1.0f - ArmorPenetration) * 0.5f;
        BaseDamage *= (1.0f - ArmorDamageReduction);
        
        UE_LOG(LogTacticalNexus, VeryVerbose, TEXT("🛡️ Armor penetration applied - Final damage: %.1f"), BaseDamage);
    }
    
    // Distance falloff (minimal for M4A4)
    float Distance = FVector::Dist(GetActorLocation(), HitResult.Location);
    if (Distance > 1200.0f) // Start falloff after 12 meters
    {
        float FalloffFactor = FMath::Clamp(1.0f - ((Distance - 1200.0f) / 6800.0f), 0.80f, 1.0f);
        BaseDamage *= FalloffFactor;
    }
    
    return BaseDamage;
}
