import { EventEmitter } from 'events';
import { Vector3 } from '../shared/types';

export interface RoundConfig {
    maxRounds: number;
    roundTime: number; // em segundos
    freezeTime: number; // tempo de compra em segundos
    warmupTime: number; // tempo de aquecimento
    bombTimer: number; // tempo da bomba
    defuseTime: number; // tempo para desarmar
    c4Timer: number; // tempo do C4
}

export interface TeamScore {
    terroristRounds: number;
    counterTerroristRounds: number;
}

export interface RoundResult {
    winner: 'terrorist' | 'counter_terrorist' | 'draw';
    reason: 'elimination' | 'bomb_exploded' | 'bomb_defused' | 'time_expired' | 'hostage_rescued';
    roundNumber: number;
    duration: number;
    mvp?: string; // Player ID do MVP
}

export interface PlayerRoundStats {
    playerId: string;
    kills: number;
    deaths: number;
    assists: number;
    damage: number;
    bombPlants: number;
    bombDefuses: number;
    hostageRescues: number;
}

export type RoundPhase = 'warmup' | 'freeze_time' | 'live' | 'post_round' | 'halftime' | 'overtime' | 'match_end';

export class RoundSystem extends EventEmitter {
    private config: RoundConfig;
    private currentRound: number = 0;
    private currentPhase: RoundPhase = 'warmup';
    private phaseStartTime: number = 0;
    private teamScore: TeamScore = { terroristRounds: 0, counterTerroristRounds: 0 };
    private roundHistory: RoundResult[] = [];
    private playerStats: Map<string, PlayerRoundStats> = new Map();
    private bombPlanted: boolean = false;
    private bombPlantTime: number = 0;
    private bombSite: 'A' | 'B' | null = null;
    private isMatchActive: boolean = false;
    private sidesSwapped: boolean = false;

    // Configuração padrão do CS2
    private static readonly DEFAULT_CONFIG: RoundConfig = {
        maxRounds: 24, // MR12 (primeiro a 13)
        roundTime: 115, // 1:55
        freezeTime: 15, // 15 segundos de buy time
        warmupTime: 300, // 5 minutos de warmup
        bombTimer: 40, // 40 segundos para explodir
        defuseTime: 10, // 10 segundos para desarmar (5 com kit)
        c4Timer: 40
    };

    constructor(config?: Partial<RoundConfig>) {
        super();
        this.config = { ...RoundSystem.DEFAULT_CONFIG, ...config };
        this.initializeRoundSystem();
    }

    private initializeRoundSystem(): void {
        console.log('🎯 Sistema de Rounds inicializado');
        this.startWarmup();
    }

    public startWarmup(): void {
        this.currentPhase = 'warmup';
        this.phaseStartTime = Date.now();
        this.isMatchActive = false;
        
        console.log(`🔥 Warmup iniciado (${this.config.warmupTime}s)`);
        this.emit('phaseChanged', { 
            phase: 'warmup', 
            duration: this.config.warmupTime,
            message: 'Aquecimento - Prepare-se para a partida!'
        });

        // Timer do warmup
        setTimeout(() => {
            this.startMatch();
        }, this.config.warmupTime * 1000);
    }

    public startMatch(): void {
        this.isMatchActive = true;
        this.currentRound = 1;
        this.resetTeamScore();
        this.roundHistory = [];
        
        console.log('🚀 Partida iniciada!');
        this.emit('matchStarted', { 
            maxRounds: this.config.maxRounds,
            roundTime: this.config.roundTime
        });

        this.startRound();
    }

    public startRound(): void {
        if (!this.isMatchActive) return;

        // Verifica se precisa trocar de lado (após round 12 no MR12)
        if (this.currentRound === 13 && !this.sidesSwapped) {
            this.swapSides();
            return;
        }

        // Verifica condições de vitória
        if (this.checkMatchEnd()) {
            return;
        }

        this.currentPhase = 'freeze_time';
        this.phaseStartTime = Date.now();
        this.bombPlanted = false;
        this.bombSite = null;
        
        // Reset das estatísticas do round
        this.resetRoundStats();

        console.log(`🎯 Round ${this.currentRound} - Freeze Time (${this.config.freezeTime}s)`);
        this.emit('roundStarted', {
            roundNumber: this.currentRound,
            phase: 'freeze_time',
            freezeTime: this.config.freezeTime,
            terroristScore: this.teamScore.terroristRounds,
            counterTerroristScore: this.teamScore.counterTerroristRounds
        });

        // Timer do freeze time
        setTimeout(() => {
            this.startLiveRound();
        }, this.config.freezeTime * 1000);
    }

    private startLiveRound(): void {
        this.currentPhase = 'live';
        this.phaseStartTime = Date.now();

        console.log(`⚡ Round ${this.currentRound} - LIVE! (${this.config.roundTime}s)`);
        this.emit('roundLive', {
            roundNumber: this.currentRound,
            roundTime: this.config.roundTime
        });

        // Timer do round
        setTimeout(() => {
            if (this.currentPhase === 'live') {
                this.endRound('counter_terrorist', 'time_expired');
            }
        }, this.config.roundTime * 1000);
    }

    public plantBomb(playerId: string, site: 'A' | 'B', position: Vector3): boolean {
        if (this.currentPhase !== 'live' || this.bombPlanted) {
            return false;
        }

        this.bombPlanted = true;
        this.bombPlantTime = Date.now();
        this.bombSite = site;

        // Adiciona estatística de plant
        const stats = this.getPlayerStats(playerId);
        stats.bombPlants++;

        console.log(`💣 Bomba plantada no site ${site} por ${playerId}`);
        this.emit('bombPlanted', {
            playerId,
            site,
            position,
            timer: this.config.bombTimer
        });

        // Timer da bomba
        setTimeout(() => {
            if (this.bombPlanted && this.currentPhase === 'live') {
                this.endRound('terrorist', 'bomb_exploded');
            }
        }, this.config.bombTimer * 1000);

        return true;
    }

    public defuseBomb(playerId: string, hasDefuseKit: boolean = false): boolean {
        if (!this.bombPlanted || this.currentPhase !== 'live') {
            return false;
        }

        const defuseTime = hasDefuseKit ? this.config.defuseTime / 2 : this.config.defuseTime;
        
        console.log(`🛡️ ${playerId} começou a desarmar a bomba (${defuseTime}s)`);
        this.emit('defuseStarted', { playerId, defuseTime, hasKit: hasDefuseKit });

        // Simula tempo de defuse (em um jogo real, isso seria interrompível)
        setTimeout(() => {
            if (this.bombPlanted && this.currentPhase === 'live') {
                this.bombPlanted = false;
                
                // Adiciona estatística de defuse
                const stats = this.getPlayerStats(playerId);
                stats.bombDefuses++;

                this.endRound('counter_terrorist', 'bomb_defused');
            }
        }, defuseTime * 1000);

        return true;
    }

    public eliminatePlayer(playerId: string, killerId?: string): void {
        const stats = this.getPlayerStats(playerId);
        stats.deaths++;

        if (killerId) {
            const killerStats = this.getPlayerStats(killerId);
            killerStats.kills++;
        }

        this.emit('playerEliminated', { playerId, killerId });

        // Verifica se o round acabou por eliminação
        this.checkRoundEndByElimination();
    }

    private checkRoundEndByElimination(): void {
        // Esta lógica seria implementada com base no estado dos jogadores vivos
        // Por enquanto, é um placeholder
        console.log('🔍 Verificando fim de round por eliminação...');
    }

    private getPlayerStats(playerId: string): PlayerRoundStats {
        if (!this.playerStats.has(playerId)) {
            this.playerStats.set(playerId, {
                playerId,
                kills: 0,
                deaths: 0,
                assists: 0,
                damage: 0,
                bombPlants: 0,
                bombDefuses: 0,
                hostageRescues: 0
            });
        }
        return this.playerStats.get(playerId)!;
    }

    private resetRoundStats(): void {
        this.playerStats.clear();
    }

    private resetTeamScore(): void {
        this.teamScore = { terroristRounds: 0, counterTerroristRounds: 0 };
    }

    private swapSides(): void {
        this.sidesSwapped = true;
        this.currentPhase = 'halftime';
        
        console.log('🔄 Halftime - Trocando de lado!');
        this.emit('halftime', {
            terroristScore: this.teamScore.terroristRounds,
            counterTerroristScore: this.teamScore.counterTerroristRounds
        });

        // Continua para o próximo round após 5 segundos
        setTimeout(() => {
            this.startRound();
        }, 5000);
    }

    private checkMatchEnd(): boolean {
        const maxRoundsToWin = Math.ceil(this.config.maxRounds / 2);
        
        if (this.teamScore.terroristRounds >= maxRoundsToWin || 
            this.teamScore.counterTerroristRounds >= maxRoundsToWin) {
            this.endMatch();
            return true;
        }

        return false;
    }

    private endMatch(): void {
        this.currentPhase = 'match_end';
        this.isMatchActive = false;

        const winner = this.teamScore.terroristRounds > this.teamScore.counterTerroristRounds 
            ? 'terrorist' : 'counter_terrorist';

        console.log(`🏆 Partida finalizada! Vencedor: ${winner}`);
        this.emit('matchEnded', {
            winner,
            finalScore: this.teamScore,
            roundHistory: this.roundHistory
        });
    }

    public endRound(winner: 'terrorist' | 'counter_terrorist', reason: string): void {
        if (this.currentPhase !== 'live') return;

        this.currentPhase = 'post_round';
        const roundDuration = (Date.now() - this.phaseStartTime) / 1000;

        // Atualiza score
        if (winner === 'terrorist') {
            this.teamScore.terroristRounds++;
        } else {
            this.teamScore.counterTerroristRounds++;
        }

        const roundResult: RoundResult = {
            winner,
            reason: reason as any,
            roundNumber: this.currentRound,
            duration: roundDuration
        };

        this.roundHistory.push(roundResult);

        console.log(`🎯 Round ${this.currentRound} finalizado - Vencedor: ${winner} (${reason})`);
        this.emit('roundEnded', {
            ...roundResult,
            newScore: this.teamScore,
            playerStats: Array.from(this.playerStats.values())
        });

        // Próximo round após 5 segundos
        setTimeout(() => {
            this.currentRound++;
            this.startRound();
        }, 5000);
    }

    // Getters públicos
    public getCurrentRound(): number { return this.currentRound; }
    public getCurrentPhase(): RoundPhase { return this.currentPhase; }
    public getTeamScore(): TeamScore { return { ...this.teamScore }; }
    public getRoundHistory(): RoundResult[] { return [...this.roundHistory]; }
    public isBombPlanted(): boolean { return this.bombPlanted; }
    public getBombSite(): 'A' | 'B' | null { return this.bombSite; }
    public isInFreezeTime(): boolean { return this.currentPhase === 'freeze_time'; }
    public isRoundLive(): boolean { return this.currentPhase === 'live'; }
}
