// Copyright Tactical Nexus Team. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/PlayerController.h"
#include "Engine/DataTable.h"
#include "Net/UnrealNetwork.h"
#include "TacticalNexusGameMode.h"
#include "TacticalNexusPlayerController.generated.h"

USTRUCT(BlueprintType)
struct FPlayerSettings
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Settings")
    float MouseSensitivity;

    UPROPERTY(BlueprintReadWrite, Category = "Settings")
    float FOV;

    UPROPERTY(BlueprintReadWrite, Category = "Settings")
    bool bInvertMouseY;

    UPROPERTY(BlueprintReadWrite, Category = "Settings")
    float MasterVolume;

    UPROPERTY(BlueprintReadWrite, Category = "Settings")
    float SFXVolume;

    UPROPERTY(BlueprintReadWrite, Category = "Settings")
    float MusicVolume;

    UPROPERTY(BlueprintReadWrite, Category = "Settings")
    int32 GraphicsQuality;

    FPlayerSettings()
    {
        MouseSensitivity = 1.0f;
        FOV = 90.0f;
        bInvertMouseY = false;
        MasterVolume = 1.0f;
        SFXVolume = 1.0f;
        MusicVolume = 0.7f;
        GraphicsQuality = 3; // High quality
    }
};

USTRUCT(BlueprintType)
struct FPlayerProfile
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Profile")
    FString PlayerName;

    UPROPERTY(BlueprintReadWrite, Category = "Profile")
    int32 Level;

    UPROPERTY(BlueprintReadWrite, Category = "Profile")
    int32 Experience;

    UPROPERTY(BlueprintReadWrite, Category = "Profile")
    int32 CompetitiveRank;

    UPROPERTY(BlueprintReadWrite, Category = "Profile")
    int32 TotalKills;

    UPROPERTY(BlueprintReadWrite, Category = "Profile")
    int32 TotalDeaths;

    UPROPERTY(BlueprintReadWrite, Category = "Profile")
    int32 MatchesWon;

    UPROPERTY(BlueprintReadWrite, Category = "Profile")
    int32 MatchesLost;

    UPROPERTY(BlueprintReadWrite, Category = "Profile")
    float KDRatio;

    UPROPERTY(BlueprintReadWrite, Category = "Profile")
    float WinRate;

    FPlayerProfile()
    {
        PlayerName = TEXT("Player");
        Level = 1;
        Experience = 0;
        CompetitiveRank = 0;
        TotalKills = 0;
        TotalDeaths = 0;
        MatchesWon = 0;
        MatchesLost = 0;
        KDRatio = 0.0f;
        WinRate = 0.0f;
    }
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnTeamAssigned, ETeam, NewTeam);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnMoneyChanged, int32, NewAmount, int32, Change);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnBuyMenuToggled, bool, bIsOpen);

UCLASS(Blueprintable)
class TACTICALNEXUSUE5_API ATacticalNexusPlayerController : public APlayerController
{
    GENERATED_BODY()

public:
    ATacticalNexusPlayerController();

protected:
    virtual void BeginPlay() override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    virtual void Tick(float DeltaTime) override;
    virtual void SetupInputComponent() override;

    // Player Profile
    UPROPERTY(Replicated, BlueprintReadOnly, Category = "Player Profile")
    FPlayerProfile PlayerProfile;

    UPROPERTY(BlueprintReadOnly, Category = "Player Settings")
    FPlayerSettings PlayerSettings;

    // Team Assignment
    UPROPERTY(ReplicatedUsing = OnRep_AssignedTeam, BlueprintReadOnly, Category = "Team")
    ETeam AssignedTeam;

    UPROPERTY(Replicated, BlueprintReadOnly, Category = "Team")
    bool bIsTeamLeader;

    // Match State
    UPROPERTY(BlueprintReadOnly, Category = "Match")
    bool bIsSpectating;

    UPROPERTY(BlueprintReadOnly, Category = "Match")
    bool bIsBuyMenuOpen;

    UPROPERTY(BlueprintReadOnly, Category = "Match")
    bool bIsScoreboardOpen;

    UPROPERTY(BlueprintReadOnly, Category = "Match")
    bool bIsChatOpen;

    // Events
    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnTeamAssigned OnTeamAssigned;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnMoneyChanged OnMoneyChanged;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnBuyMenuToggled OnBuyMenuToggled;

    // Input Functions
    UFUNCTION(BlueprintCallable, Category = "Input")
    void ToggleBuyMenu();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void ToggleScoreboard();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void OpenChat();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void OpenTeamChat();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void CallVote(const FString& VoteType, const FString& Target);

    // Team Functions
    UFUNCTION(BlueprintCallable, Category = "Team")
    void RequestTeamChange(ETeam NewTeam);

    UFUNCTION(BlueprintCallable, Category = "Team")
    void SetTeamLeader(bool bLeader);

    // Buy System
    UFUNCTION(BlueprintCallable, Category = "Buy System")
    bool PurchaseWeapon(int32 WeaponID, int32 Price);

    UFUNCTION(BlueprintCallable, Category = "Buy System")
    bool PurchaseEquipment(int32 EquipmentID, int32 Price);

    UFUNCTION(BlueprintCallable, Category = "Buy System")
    void SellWeapon(int32 WeaponID);

    // Communication
    UFUNCTION(BlueprintCallable, Category = "Communication")
    void SendChatMessage(const FString& Message, bool bTeamOnly = false);

    UFUNCTION(BlueprintCallable, Category = "Communication")
    void SendVoiceCommand(int32 CommandID);

    // Settings
    UFUNCTION(BlueprintCallable, Category = "Settings")
    void UpdatePlayerSettings(const FPlayerSettings& NewSettings);

    UFUNCTION(BlueprintCallable, Category = "Settings")
    void SavePlayerProfile();

    UFUNCTION(BlueprintCallable, Category = "Settings")
    void LoadPlayerProfile();

    // Statistics
    UFUNCTION(BlueprintCallable, Category = "Statistics")
    void UpdateKillStats(int32 Kills, int32 Deaths, int32 Assists);

    UFUNCTION(BlueprintCallable, Category = "Statistics")
    void UpdateMatchStats(bool bWon);

    // Spectating
    UFUNCTION(BlueprintCallable, Category = "Spectating")
    void StartSpectating();

    UFUNCTION(BlueprintCallable, Category = "Spectating")
    void StopSpectating();

    UFUNCTION(BlueprintCallable, Category = "Spectating")
    void SpectateNextPlayer();

    UFUNCTION(BlueprintCallable, Category = "Spectating")
    void SpectatePreviousPlayer();

    // Server Functions
    UFUNCTION(Server, Reliable, WithValidation)
    void ServerRequestTeamChange(ETeam NewTeam);

    UFUNCTION(Server, Reliable, WithValidation)
    void ServerPurchaseWeapon(int32 WeaponID, int32 Price);

    UFUNCTION(Server, Reliable, WithValidation)
    void ServerSendChatMessage(const FString& Message, bool bTeamOnly);

    UFUNCTION(Server, Reliable, WithValidation)
    void ServerCallVote(const FString& VoteType, const FString& Target);

    // Client Functions
    UFUNCTION(Client, Reliable)
    void ClientShowBuyMenu();

    UFUNCTION(Client, Reliable)
    void ClientHideBuyMenu();

    UFUNCTION(Client, Reliable)
    void ClientDisplayChatMessage(const FString& PlayerName, const FString& Message, bool bTeamOnly);

    UFUNCTION(Client, Reliable)
    void ClientShowNotification(const FString& Message, float Duration);

    // Replication Functions
    UFUNCTION()
    void OnRep_AssignedTeam();

protected:
    // Input binding functions
    void OnBuyMenuPressed();
    void OnScoreboardPressed();
    void OnScoreboardReleased();
    void OnChatPressed();
    void OnTeamChatPressed();
    void OnVoteYes();
    void OnVoteNo();

    // Internal functions
    void UpdatePlayerStatistics();
    void CalculateKDRatio();
    void CalculateWinRate();
    void ApplyPlayerSettings();

private:
    // Internal state
    TArray<APlayerController*> SpectateTargets;
    int32 CurrentSpectateIndex;
    
    // UI state
    bool bScoreboardPressed;
    
    // Chat system
    TArray<FString> ChatHistory;
    int32 MaxChatHistory;
    
    // Vote system
    bool bHasVoted;
    FString CurrentVoteType;
    FString CurrentVoteTarget;
};
