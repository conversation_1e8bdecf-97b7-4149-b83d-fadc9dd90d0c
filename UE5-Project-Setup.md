# 🎮 TACTICAL NEXUS UE5 - PROJECT SETUP GUIDE

## 🚀 **FASE 1: CRIAÇÃO DO PROJETO UE5**

### **1.1 Configuração Inicial do Projeto**

#### **Especificações do Projeto:**
```
Nome: TacticalNexusUE5
Template: First Person (C++)
Target Platform: Windows (Steam)
Quality Preset: Maximum
Raytracing: Enabled
```

#### **Plugins Essenciais:**
```
Core Plugins:
- Online Subsystem Steam ✅
- Easy Anti Cheat ✅
- Chaos Physics ✅
- Niagara VFX ✅
- MetaHuman ✅

Networking:
- Replication Graph ✅
- Network Prediction ✅
- Steam Sockets ✅

Audio:
- Steam Audio ✅
- Audio Synesthesia ✅
- Synthesis and DSP Effects ✅

Performance:
- DLSS/FSR ✅
- Lumen ✅
- Nanite ✅
```

### **1.2 Estrutura de Pastas**

```
TacticalNexusUE5/
├── Content/
│   ├── Core/
│   │   ├── GameModes/
│   │   ├── PlayerControllers/
│   │   ├── GameStates/
│   │   └── PlayerStates/
│   ├── Weapons/
│   │   ├── Rifles/
│   │   ├── Pistols/
│   │   ├── Grenades/
│   │   └── Animations/
│   ├── Maps/
│   │   ├── Dust2/
│   │   ├── Mirage/
│   │   ├── Inferno/
│   │   ├── Cache/
│   │   └── Overpass/
│   ├── UI/
│   │   ├── MainMenu/
│   │   ├── HUD/
│   │   ├── BuyMenu/
│   │   └── Scoreboard/
│   ├── Audio/
│   │   ├── Weapons/
│   │   ├── Footsteps/
│   │   ├── Music/
│   │   └── Ambient/
│   ├── VFX/
│   │   ├── MuzzleFlash/
│   │   ├── Impacts/
│   │   ├── Explosions/
│   │   └── Smoke/
│   └── Materials/
│       ├── Weapons/
│       ├── Environment/
│       └── Characters/
├── Source/
│   ├── TacticalNexusUE5/
│   │   ├── Core/
│   │   ├── Weapons/
│   │   ├── Networking/
│   │   ├── Audio/
│   │   └── Utils/
│   └── TacticalNexusUE5Server/
└── Config/
    ├── DefaultEngine.ini
    ├── DefaultGame.ini
    └── DefaultInput.ini
```

### **1.3 Configurações de Engine (DefaultEngine.ini)**

```ini
[/Script/Engine.Engine]
+ActiveGameNameRedirects=(OldGameName="TP_FirstPersonBP",NewGameName="/Script/TacticalNexusUE5")
+ActiveGameNameRedirects=(OldGameName="/Script/TP_FirstPersonBP",NewGameName="/Script/TacticalNexusUE5")

[/Script/HardwareTargeting.HardwareTargetingSettings]
TargetedHardwareClass=Desktop
AppliedTargetedHardwareClass=Desktop
DefaultGraphicsPerformance=Maximum
AppliedDefaultGraphicsPerformance=Maximum

[/Script/Engine.RendererSettings]
r.GenerateMeshDistanceFields=True
r.DynamicGlobalIlluminationMethod=1
r.ReflectionMethod=1
r.Shadow.Virtual.Enable=1
r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange=True
r.DefaultFeature.LocalExposure.HighlightContrastScale=0.8
r.DefaultFeature.LocalExposure.ShadowContrastScale=0.8
r.AntiAliasing=3
r.MSAACount=8

[/Script/WorldPartition.WorldPartitionSubsystem]
EnableStreaming=True

[/Script/Engine.NetworkSettings]
n.VerifyPeer=False

[/Script/OnlineSubsystemSteam.SteamNetDriver]
NetConnectionClassName="OnlineSubsystemSteam.SteamNetConnection"

[OnlineSubsystem]
DefaultPlatformService=Steam

[OnlineSubsystemSteam]
bEnabled=true
SteamDevAppId=480
bInitServerOnClient=true

[/Script/Engine.GameEngine]
+NetDriverDefinitions=(DefName="GameNetDriver",DriverClassName="OnlineSubsystemSteam.SteamNetDriver",DriverClassNameFallback="OnlineSubsystemUtils.IpNetDriver")

[/Script/EasyAntiCheatServer.EasyAntiCheatServerSettings]
bEnabled=true
ProductId=YOUR_EAC_PRODUCT_ID
SandboxId=YOUR_EAC_SANDBOX_ID
```

### **1.4 Configurações de Game (DefaultGame.ini)**

```ini
[/Script/EngineSettings.GeneralProjectSettings]
ProjectID=TACTICAL_NEXUS_UE5_PROJECT_ID
ProjectName=Tactical Nexus
ProjectVersion=1.0.0
CompanyName=Tactical Nexus Team
CompanyDistinguishedName=CN=Tactical Nexus Team
Homepage="https://tactical-nexus.com"
SupportContact="<EMAIL>"

[/Script/UnrealEd.ProjectPackagingSettings]
Build=IfProjectHasCode
BuildConfiguration=PPBC_Shipping
BuildTarget=
StagingDirectory=(Path="")
FullRebuild=False
ForDistribution=True
IncludeDebugFiles=False
BlueprintNativizationMethod=Disabled
bIncludeNativizedAssetsInProjectGeneration=False
bExcludeMonolithicEngineHeadersInNativizedCode=False
UsePakFile=True
bUseIoStore=True
bUseZenStore=False
bMakeBinaryConfig=False
bGenerateChunks=False
bGenerateNoChunks=False
bChunkHardReferencesOnly=False
bForceOneChunkPerFile=False
MaxChunkSize=0
BuildCookRunArguments=
CookMapsOnly=False
CookAll=False
CookMapsInSingleProcess=False
bCompressed=True
PackageCompressionFormat=Oodle
bForceUseProjectBasedCompressionSettings=False
PackageAdditionalCompressionOptions=
PackageCompressionMethod=Kraken
PackageCompressionLevel_DebugDevelopment=4
PackageCompressionLevel_TestShipping=7
PackageCompressionLevel_Distribution=9
HttpChunkInstallDataDirectory=
HttpChunkInstallDataVersion=
IncludePrerequisites=True
IncludeAppLocalPrerequisites=False
bShareMaterialShaderCode=True
bDeterministicShaderCodeOrder=False
bSharedMaterialNativeLibraries=True
ApplocalPrerequisitesDirectory=(Path="")
IncludeCrashReporter=False
InternationalizationPreset=English
-CulturesToStage=en
+CulturesToStage=en
+CulturesToStage=pt-BR
LocalizationTargetCatchAllChunkId=0
bCookAll=False
bCookMapsOnly=False
bSkipEditorContent=False
bSkipMovies=False
-IniKeyBlacklist=KeyStorePassword
-IniKeyBlacklist=KeyPassword
-IniKeyBlacklist=rsa.privateexp
-IniKeyBlacklist=rsa.modulus
-IniKeyBlacklist=rsa.publicexp
-IniKeyBlacklist=aes.key
-IniKeyBlacklist=SigningPublicExponent
-IniKeyBlacklist=SigningModulus
-IniKeyBlacklist=SigningPrivateExponent
-IniKeyBlacklist=EncryptionKey
-IniKeyBlacklist=DevCenterUsername
-IniKeyBlacklist=DevCenterPassword
-IniKeyBlacklist=IOSTeamID
-IniKeyBlacklist=SigningCertificate
-IniKeyBlacklist=MobileProvision
-IniKeyBlacklist=IniKeyBlacklist
-IniKeyBlacklist=IniSectionBlacklist
+IniKeyBlacklist=KeyStorePassword
+IniKeyBlacklist=KeyPassword
+IniKeyBlacklist=rsa.privateexp
+IniKeyBlacklist=rsa.modulus
+IniKeyBlacklist=rsa.publicexp
+IniKeyBlacklist=aes.key
+IniKeyBlacklist=SigningPublicExponent
+IniKeyBlacklist=SigningModulus
+IniKeyBlacklist=SigningPrivateExponent
+IniKeyBlacklist=EncryptionKey
+IniKeyBlacklist=DevCenterUsername
+IniKeyBlacklist=DevCenterPassword
+IniKeyBlacklist=IOSTeamID
+IniKeyBlacklist=SigningCertificate
+IniKeyBlacklist=MobileProvision
+IniKeyBlacklist=IniKeyBlacklist
+IniKeyBlacklist=IniSectionBlacklist
+MapsToCook=(FilePath="/Game/Maps/Dust2")
+MapsToCook=(FilePath="/Game/Maps/Mirage")
+MapsToCook=(FilePath="/Game/Maps/MainMenu")

[/Script/Engine.AssetManagerSettings]
-PrimaryAssetTypesToScan=(PrimaryAssetType="Map",AssetBaseClass=/Script/Engine.World,bHasBlueprintClasses=False,bIsEditorOnly=True,Directories=((Path="/Game/Maps")),SpecificAssets=,Rules=(Priority=-1,ChunkId=-1,bApplyRecursively=True,CookRule=Unknown))
+PrimaryAssetTypesToScan=(PrimaryAssetType="Map",AssetBaseClass=/Script/Engine.World,bHasBlueprintClasses=False,bIsEditorOnly=True,Directories=((Path="/Game/Maps")),SpecificAssets=,Rules=(Priority=-1,ChunkId=-1,bApplyRecursively=True,CookRule=AlwaysCook))
+PrimaryAssetTypesToScan=(PrimaryAssetType="PrimaryAssetLabel",AssetBaseClass=/Script/Engine.PrimaryAssetLabel,bHasBlueprintClasses=False,bIsEditorOnly=True,Directories=((Path="/Game")),SpecificAssets=,Rules=(Priority=-1,ChunkId=-1,bApplyRecursively=True,CookRule=Unknown))
bOnlyCookProductionAssets=False
bShouldManagerDetermineTypeAndName=False
bShouldGuessTypeAndNameInEditor=True
bShouldAcquireMissingChunksOnLoad=False
bShouldWarnAboutInvalidAssets=True
MetaDataTagsForAssetRegistry=()
```

### **1.5 Input Configuration (DefaultInput.ini)**

```ini
[/Script/Engine.InputSettings]
-AxisConfig=(AxisKeyName="Gamepad_LeftX",AxisProperties=(DeadZone=0.25,Exponent=1.f,Sensitivity=1.f))
-AxisConfig=(AxisKeyName="Gamepad_LeftY",AxisProperties=(DeadZone=0.25,Exponent=1.f,Sensitivity=1.f))
-AxisConfig=(AxisKeyName="Gamepad_RightX",AxisProperties=(DeadZone=0.25,Exponent=1.f,Sensitivity=1.f))
-AxisConfig=(AxisKeyName="Gamepad_RightY",AxisProperties=(DeadZone=0.25,Exponent=1.f,Sensitivity=1.f))
-AxisConfig=(AxisKeyName="MouseX",AxisProperties=(DeadZone=0.f,Exponent=1.f,Sensitivity=0.07f))
-AxisConfig=(AxisKeyName="MouseY",AxisProperties=(DeadZone=0.f,Exponent=1.f,Sensitivity=0.07f))
-AxisConfig=(AxisKeyName="Mouse2D",AxisProperties=(DeadZone=0.f,Exponent=1.f,Sensitivity=0.07f))
+AxisConfig=(AxisKeyName="MouseY",AxisProperties=(DeadZone=0.000000,Sensitivity=0.070000,Exponent=1.000000,bInvert=False))
+AxisConfig=(AxisKeyName="MouseX",AxisProperties=(DeadZone=0.000000,Sensitivity=0.070000,Exponent=1.000000,bInvert=False))
+AxisConfig=(AxisKeyName="Mouse2D",AxisProperties=(DeadZone=0.000000,Sensitivity=0.070000,Exponent=1.000000,bInvert=False))
+AxisConfig=(AxisKeyName="MouseWheelAxis",AxisProperties=(DeadZone=0.000000,Sensitivity=1.000000,Exponent=1.000000,bInvert=False))
+AxisConfig=(AxisKeyName="Gamepad_LeftX",AxisProperties=(DeadZone=0.250000,Sensitivity=1.000000,Exponent=1.000000,bInvert=False))
+AxisConfig=(AxisKeyName="Gamepad_LeftY",AxisProperties=(DeadZone=0.250000,Sensitivity=1.000000,Exponent=1.000000,bInvert=False))
+AxisConfig=(AxisKeyName="Gamepad_RightX",AxisProperties=(DeadZone=0.250000,Sensitivity=1.000000,Exponent=1.000000,bInvert=False))
+AxisConfig=(AxisKeyName="Gamepad_RightY",AxisProperties=(DeadZone=0.250000,Sensitivity=1.000000,Exponent=1.000000,bInvert=False))
bAltEnterTogglesFullscreen=True
bF11TogglesFullscreen=True
bUseMouseForTouch=False
bEnableMouseSmoothing=True
bEnableFOVScaling=True
bCaptureMouseOnLaunch=True
bAlwaysShowTouchInterface=False
bShowConsoleOnFourFingerTap=True
bEnableGestureRecognizer=False
bUseAutocorrect=False
DefaultViewportMouseCaptureMode=CapturePermanently_IncludingInitialMouseDown
DefaultViewportMouseLockMode=LockOnCapture
FOVScale=0.011110
DoubleClickTime=0.200000
+ActionMappings=(ActionName="Jump",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=SpaceBar)
+ActionMappings=(ActionName="Fire",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=LeftMouseButton)
+ActionMappings=(ActionName="ADS",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=RightMouseButton)
+ActionMappings=(ActionName="Reload",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=R)
+ActionMappings=(ActionName="Crouch",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=LeftControl)
+ActionMappings=(ActionName="Walk",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=LeftShift)
+ActionMappings=(ActionName="Use",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=E)
+ActionMappings=(ActionName="Drop",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=G)
+ActionMappings=(ActionName="BuyMenu",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=B)
+ActionMappings=(ActionName="Scoreboard",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=Tab)
+ActionMappings=(ActionName="Chat",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=Y)
+ActionMappings=(ActionName="TeamChat",bShift=False,bCtrl=False,bAlt=False,bCmd=False,Key=U)
+AxisMappings=(AxisName="Move Forward / Backward",Scale=1.000000,Key=W)
+AxisMappings=(AxisName="Move Forward / Backward",Scale=-1.000000,Key=S)
+AxisMappings=(AxisName="Move Right / Left",Scale=-1.000000,Key=A)
+AxisMappings=(AxisName="Move Right / Left",Scale=1.000000,Key=D)
+AxisMappings=(AxisName="Turn Right / Left Gamepad",Scale=1.000000,Key=Gamepad_RightX)
+AxisMappings=(AxisName="Turn Right / Left Mouse",Scale=1.000000,Key=MouseX)
+AxisMappings=(AxisName="Look Up / Down Gamepad",Scale=1.000000,Key=Gamepad_RightY)
+AxisMappings=(AxisName="Look Up / Down Mouse",Scale=-1.000000,Key=MouseY)
DefaultPlayerInputClass=/Script/Engine.PlayerInput
DefaultInputComponentClass=/Script/Engine.InputComponent
DefaultTouchInterface=/Engine/MobileResources/HUD/DefaultVirtualJoysticks.DefaultVirtualJoysticks
-ConsoleKeys=Tilde
+ConsoleKeys=Tilde
+ConsoleKeys=Caret
```

---

## 🎯 **PRÓXIMOS PASSOS**

1. **Criar projeto UE5** com essas configurações
2. **Configurar plugins** essenciais
3. **Setup Steam integration** básica
4. **Criar classes base** C++
5. **Implementar weapon system** básico

**🚀 READY TO START UE5 DEVELOPMENT! 🚀**
