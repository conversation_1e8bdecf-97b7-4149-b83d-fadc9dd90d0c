using UnrealBuildTool;

public class TacticalNexusUE5 : ModuleRules
{
	public TacticalNexusUE5(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;

		PublicDependencyModuleNames.AddRange(new string[] { 
			"Core", 
			"CoreUObject", 
			"Engine", 
			"InputCore",
			"OnlineSubsystem",
			"OnlineSubsystemSteam",
			"Niagara",
			"AudioMixer",
			"UMG",
			"Slate",
			"SlateCore",
			"ReplicationGraph",
			"NetworkPrediction"
		});

		PrivateDependencyModuleNames.AddRange(new string[] { 
			"Steamworks"
		});

		// Steam integration
		if (Target.Platform == UnrealTargetPlatform.Win64)
		{
			PublicDefinitions.Add("STEAM_ENABLED=1");
		}
		else
		{
			PublicDefinitions.Add("STEAM_ENABLED=0");
		}

		// Performance optimizations
		bUseUnityBuild = true;
		bUsePCHFiles = true;
		bUseSharedPCHs = true;
	}
}
