// Copyright Tactical Nexus Team. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Character.h"
#include "Components/CapsuleComponent.h"
#include "Camera/CameraComponent.h"
#include "Components/InputComponent.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "GameFramework/SpringArmComponent.h"
#include "Sound/SoundCue.h"
#include "Components/AudioComponent.h"
#include "Net/UnrealNetwork.h"
#include "Engine/DataTable.h"
#include "../Weapons/BaseWeapon.h"
#include "TacticalNexusCharacter.generated.h"

UENUM(BlueprintType)
enum class EPlayerTeam : uint8
{
    None            UMETA(DisplayName = "None"),
    Terrorist       UMETA(DisplayName = "Terrorist"),
    CounterTerrorist UMETA(DisplayName = "Counter-Terrorist"),
    Spectator       UMETA(DisplayName = "Spectator")
};

UENUM(BlueprintType)
enum class EPlayerState : uint8
{
    Alive           UMETA(DisplayName = "Alive"),
    Dead            UMETA(DisplayName = "Dead"),
    Spectating      UMETA(DisplayName = "Spectating"),
    Connecting      UMETA(DisplayName = "Connecting")
};

USTRUCT(BlueprintType)
struct FPlayerStats
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Player Stats")
    int32 Kills;

    UPROPERTY(BlueprintReadWrite, Category = "Player Stats")
    int32 Deaths;

    UPROPERTY(BlueprintReadWrite, Category = "Player Stats")
    int32 Assists;

    UPROPERTY(BlueprintReadWrite, Category = "Player Stats")
    int32 Score;

    UPROPERTY(BlueprintReadWrite, Category = "Player Stats")
    int32 MVPRounds;

    UPROPERTY(BlueprintReadWrite, Category = "Player Stats")
    float Accuracy;

    UPROPERTY(BlueprintReadWrite, Category = "Player Stats")
    int32 HeadShots;

    FPlayerStats()
    {
        Kills = 0;
        Deaths = 0;
        Assists = 0;
        Score = 0;
        MVPRounds = 0;
        Accuracy = 0.0f;
        HeadShots = 0;
    }
};

USTRUCT(BlueprintType)
struct FPlayerEconomy
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Economy")
    int32 Money;

    UPROPERTY(BlueprintReadWrite, Category = "Economy")
    int32 MoneySpentThisRound;

    UPROPERTY(BlueprintReadWrite, Category = "Economy")
    bool bCanBuy;

    FPlayerEconomy()
    {
        Money = 800; // CS2 starting money
        MoneySpentThisRound = 0;
        bCanBuy = true;
    }
};

UCLASS(Blueprintable)
class TACTICALNEXUSUE5_API ATacticalNexusCharacter : public ACharacter
{
    GENERATED_BODY()

public:
    ATacticalNexusCharacter();

protected:
    virtual void BeginPlay() override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    virtual void Tick(float DeltaTime) override;
    virtual void SetupPlayerInputComponent(class UInputComponent* PlayerInputComponent) override;

    // Camera Components
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Camera")
    UCameraComponent* FirstPersonCamera;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Camera")
    USkeletalMeshComponent* FirstPersonMesh;

    // Player State
    UPROPERTY(ReplicatedUsing = OnRep_PlayerTeam, BlueprintReadOnly, Category = "Player State")
    EPlayerTeam PlayerTeam;

    UPROPERTY(ReplicatedUsing = OnRep_PlayerState, BlueprintReadOnly, Category = "Player State")
    EPlayerState CurrentPlayerState;

    UPROPERTY(ReplicatedUsing = OnRep_Health, BlueprintReadOnly, Category = "Player State")
    float Health;

    UPROPERTY(Replicated, BlueprintReadOnly, Category = "Player State")
    float MaxHealth;

    UPROPERTY(ReplicatedUsing = OnRep_Armor, BlueprintReadOnly, Category = "Player State")
    float Armor;

    UPROPERTY(Replicated, BlueprintReadOnly, Category = "Player State")
    float MaxArmor;

    // Player Stats
    UPROPERTY(Replicated, BlueprintReadOnly, Category = "Player Stats")
    FPlayerStats PlayerStats;

    UPROPERTY(Replicated, BlueprintReadOnly, Category = "Economy")
    FPlayerEconomy PlayerEconomy;

    // Weapons
    UPROPERTY(ReplicatedUsing = OnRep_CurrentWeapon, BlueprintReadOnly, Category = "Weapons")
    ABaseWeapon* CurrentWeapon;

    UPROPERTY(Replicated, BlueprintReadOnly, Category = "Weapons")
    TArray<ABaseWeapon*> WeaponInventory;

    UPROPERTY(Replicated, BlueprintReadOnly, Category = "Weapons")
    int32 CurrentWeaponIndex;

    // Movement
    UPROPERTY(BlueprintReadOnly, Category = "Movement")
    bool bIsWalking;

    UPROPERTY(BlueprintReadOnly, Category = "Movement")
    bool bIsRunning;

    UPROPERTY(BlueprintReadOnly, Category = "Movement")
    bool bIsAiming;

    // Audio
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    USoundCue* FootstepSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    USoundCue* JumpSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    USoundCue* LandSound;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Audio")
    UAudioComponent* FootstepAudioComponent;

    // Input Functions
    UFUNCTION(BlueprintCallable, Category = "Input")
    void MoveForward(float Value);

    UFUNCTION(BlueprintCallable, Category = "Input")
    void MoveRight(float Value);

    UFUNCTION(BlueprintCallable, Category = "Input")
    void Turn(float Value);

    UFUNCTION(BlueprintCallable, Category = "Input")
    void LookUp(float Value);

    UFUNCTION(BlueprintCallable, Category = "Input")
    void StartJump();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void StopJump();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void StartCrouch();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void StopCrouch();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void StartWalk();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void StopWalk();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void StartFire();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void StopFire();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void StartAiming();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void StopAiming();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void Reload();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void SwitchWeapon(int32 WeaponIndex);

    UFUNCTION(BlueprintCallable, Category = "Input")
    void DropWeapon();

    // Weapon Functions
    UFUNCTION(BlueprintCallable, Category = "Weapons")
    void EquipWeapon(ABaseWeapon* Weapon);

    UFUNCTION(BlueprintCallable, Category = "Weapons")
    void UnequipCurrentWeapon();

    UFUNCTION(BlueprintCallable, Category = "Weapons")
    ABaseWeapon* GetCurrentWeapon() const { return CurrentWeapon; }

    // Player Functions
    UFUNCTION(BlueprintCallable, Category = "Player")
    void SetPlayerTeam(EPlayerTeam NewTeam);

    UFUNCTION(BlueprintCallable, Category = "Player")
    void TakeDamage(float DamageAmount, const FHitResult& HitResult, AController* EventInstigator, AActor* DamageCauser);

    UFUNCTION(BlueprintCallable, Category = "Player")
    void Die(AController* Killer);

    UFUNCTION(BlueprintCallable, Category = "Player")
    void Respawn();

    // Economy Functions
    UFUNCTION(BlueprintCallable, Category = "Economy")
    bool CanAfford(int32 Price) const;

    UFUNCTION(BlueprintCallable, Category = "Economy")
    void SpendMoney(int32 Amount);

    UFUNCTION(BlueprintCallable, Category = "Economy")
    void AddMoney(int32 Amount);

    // Replication Functions
    UFUNCTION()
    void OnRep_PlayerTeam();

    UFUNCTION()
    void OnRep_PlayerState();

    UFUNCTION()
    void OnRep_Health();

    UFUNCTION()
    void OnRep_Armor();

    UFUNCTION()
    void OnRep_CurrentWeapon();

    // Server Functions
    UFUNCTION(Server, Reliable, WithValidation)
    void ServerSetPlayerTeam(EPlayerTeam NewTeam);

    UFUNCTION(Server, Reliable, WithValidation)
    void ServerEquipWeapon(ABaseWeapon* Weapon);

    UFUNCTION(Server, Reliable, WithValidation)
    void ServerDropWeapon();

    UFUNCTION(Server, Reliable, WithValidation)
    void ServerTakeDamage(float DamageAmount, const FHitResult& HitResult, AController* EventInstigator, AActor* DamageCauser);

protected:
    // Movement Settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    float WalkSpeed;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    float RunSpeed;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    float CrouchSpeed;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    float AimingSpeedMultiplier;

    // Camera Settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Camera")
    float BaseTurnRate;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Camera")
    float BaseLookUpRate;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Camera")
    float AimingFOV;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Camera")
    float DefaultFOV;

    // Internal Functions
    void UpdateMovementSpeed();
    void UpdateCameraFOV();
    void PlayFootstepSound();
    void HandleLanding();

private:
    // Input state
    bool bWantsToWalk;
    bool bWantsToRun;
    
    // Camera interpolation
    float TargetFOV;
    float CurrentFOV;
    
    // Footstep timing
    float LastFootstepTime;
    float FootstepInterval;
};
