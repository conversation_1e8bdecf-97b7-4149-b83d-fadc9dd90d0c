// Copyright Tactical Nexus Team. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/HUD.h"
#include "Engine/Canvas.h"
#include "Engine/Texture2D.h"
#include "Components/WidgetComponent.h"
#include "../Core/TacticalNexusGameMode.h"
#include "TacticalNexusHUD.generated.h"

USTRUCT(BlueprintType)
struct FHUDColors
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HUD Colors")
    FLinearColor HealthColor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HUD Colors")
    FLinearColor ArmorColor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HUD Colors")
    FLinearColor AmmoColor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HUD Colors")
    FLinearColor MoneyColor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HUD Colors")
    FLinearColor CrosshairColor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HUD Colors")
    FLinearColor TeamTerroristColor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HUD Colors")
    FLinearColor TeamCounterTerroristColor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HUD Colors")
    FLinearColor TimerColor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HUD Colors")
    FLinearColor KillFeedColor;

    FHUDColors()
    {
        HealthColor = FLinearColor::Green;
        ArmorColor = FLinearColor::Blue;
        AmmoColor = FLinearColor::White;
        MoneyColor = FLinearColor::Yellow;
        CrosshairColor = FLinearColor::White;
        TeamTerroristColor = FLinearColor(1.0f, 0.8f, 0.0f, 1.0f); // Orange
        TeamCounterTerroristColor = FLinearColor(0.0f, 0.6f, 1.0f, 1.0f); // Blue
        TimerColor = FLinearColor::White;
        KillFeedColor = FLinearColor::White;
    }
};

USTRUCT(BlueprintType)
struct FKillFeedEntry
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Kill Feed")
    FString KillerName;

    UPROPERTY(BlueprintReadWrite, Category = "Kill Feed")
    FString VictimName;

    UPROPERTY(BlueprintReadWrite, Category = "Kill Feed")
    FString WeaponName;

    UPROPERTY(BlueprintReadWrite, Category = "Kill Feed")
    bool bIsHeadshot;

    UPROPERTY(BlueprintReadWrite, Category = "Kill Feed")
    bool bIsTeamKill;

    UPROPERTY(BlueprintReadWrite, Category = "Kill Feed")
    float TimeStamp;

    UPROPERTY(BlueprintReadWrite, Category = "Kill Feed")
    ETeam KillerTeam;

    UPROPERTY(BlueprintReadWrite, Category = "Kill Feed")
    ETeam VictimTeam;

    FKillFeedEntry()
    {
        KillerName = TEXT("");
        VictimName = TEXT("");
        WeaponName = TEXT("");
        bIsHeadshot = false;
        bIsTeamKill = false;
        TimeStamp = 0.0f;
        KillerTeam = ETeam::None;
        VictimTeam = ETeam::None;
    }
};

UCLASS(Blueprintable)
class TACTICALNEXUSUE5_API ATacticalNexusHUD : public AHUD
{
    GENERATED_BODY()

public:
    ATacticalNexusHUD();

protected:
    virtual void BeginPlay() override;

public:
    virtual void DrawHUD() override;
    virtual void Tick(float DeltaTime) override;

    // HUD Drawing Functions
    UFUNCTION(BlueprintCallable, Category = "HUD")
    void DrawCrosshair();

    UFUNCTION(BlueprintCallable, Category = "HUD")
    void DrawPlayerStats();

    UFUNCTION(BlueprintCallable, Category = "HUD")
    void DrawWeaponInfo();

    UFUNCTION(BlueprintCallable, Category = "HUD")
    void DrawTeamInfo();

    UFUNCTION(BlueprintCallable, Category = "HUD")
    void DrawTimer();

    UFUNCTION(BlueprintCallable, Category = "HUD")
    void DrawKillFeed();

    UFUNCTION(BlueprintCallable, Category = "HUD")
    void DrawRadar();

    UFUNCTION(BlueprintCallable, Category = "HUD")
    void DrawBombInfo();

    UFUNCTION(BlueprintCallable, Category = "HUD")
    void DrawSpectatorInfo();

    // Kill Feed Functions
    UFUNCTION(BlueprintCallable, Category = "Kill Feed")
    void AddKillFeedEntry(const FKillFeedEntry& Entry);

    UFUNCTION(BlueprintCallable, Category = "Kill Feed")
    void ClearKillFeed();

    // HUD State Functions
    UFUNCTION(BlueprintCallable, Category = "HUD")
    void SetHUDVisible(bool bVisible);

    UFUNCTION(BlueprintCallable, Category = "HUD")
    void SetCrosshairVisible(bool bVisible);

    UFUNCTION(BlueprintCallable, Category = "HUD")
    void SetSpectatorMode(bool bSpectating);

    // Damage Indicator
    UFUNCTION(BlueprintCallable, Category = "HUD")
    void ShowDamageIndicator(FVector DamageDirection, float DamageAmount);

    // Hit Marker
    UFUNCTION(BlueprintCallable, Category = "HUD")
    void ShowHitMarker(bool bIsHeadshot = false);

protected:
    // HUD Settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HUD Settings")
    FHUDColors HUDColors;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HUD Settings")
    bool bShowHUD;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HUD Settings")
    bool bShowCrosshair;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HUD Settings")
    bool bShowKillFeed;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HUD Settings")
    bool bShowRadar;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HUD Settings")
    bool bIsSpectatorMode;

    // Crosshair Settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Crosshair")
    float CrosshairSize;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Crosshair")
    float CrosshairThickness;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Crosshair")
    float CrosshairGap;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Crosshair")
    bool bCrosshairDynamic; // Expands when moving/shooting

    // Font Settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fonts")
    UFont* HUDFont;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fonts")
    UFont* LargeFont;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fonts")
    UFont* SmallFont;

    // Textures
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Textures")
    UTexture2D* CrosshairTexture;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Textures")
    UTexture2D* HealthIcon;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Textures")
    UTexture2D* ArmorIcon;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Textures")
    UTexture2D* MoneyIcon;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Textures")
    UTexture2D* BombIcon;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Textures")
    UTexture2D* DefuseKitIcon;

    // Kill Feed
    UPROPERTY(BlueprintReadOnly, Category = "Kill Feed")
    TArray<FKillFeedEntry> KillFeedEntries;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Kill Feed")
    int32 MaxKillFeedEntries;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Kill Feed")
    float KillFeedDisplayTime;

    // Damage Indicators
    UPROPERTY(BlueprintReadOnly, Category = "Damage")
    TArray<FVector> DamageIndicators;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage")
    float DamageIndicatorDisplayTime;

    // Hit Marker
    UPROPERTY(BlueprintReadOnly, Category = "Hit Marker")
    bool bShowHitMarker;

    UPROPERTY(BlueprintReadOnly, Category = "Hit Marker")
    float HitMarkerTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hit Marker")
    float HitMarkerDisplayTime;

    UPROPERTY(BlueprintReadOnly, Category = "Hit Marker")
    bool bHitMarkerHeadshot;

private:
    // Internal state
    class ATacticalNexusCharacter* OwnerCharacter;
    class ATacticalNexusGameMode* GameMode;
    class ATacticalNexusPlayerController* OwnerController;

    // Helper functions
    void DrawText(const FString& Text, FVector2D Position, FLinearColor Color, UFont* Font = nullptr, float Scale = 1.0f);
    void DrawTexture(UTexture2D* Texture, FVector2D Position, FVector2D Size, FLinearColor Color = FLinearColor::White);
    void DrawLine(FVector2D Start, FVector2D End, FLinearColor Color, float Thickness = 1.0f);
    void DrawRect(FVector2D Position, FVector2D Size, FLinearColor Color);
    
    FString FormatTime(float TimeInSeconds);
    FString FormatMoney(int32 Money);
    void UpdateKillFeed(float DeltaTime);
    void UpdateDamageIndicators(float DeltaTime);
    void UpdateHitMarker(float DeltaTime);
};
