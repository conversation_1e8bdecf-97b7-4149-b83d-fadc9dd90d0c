/* Store Section Styles - COD Style */
.store-categories {
    display: flex;
    gap: 2px;
    margin-bottom: 30px;
    justify-content: center;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 2px;
    overflow: hidden;
}

.category-btn {
    padding: 15px 30px;
    background: rgba(20, 25, 30, 0.8);
    border: none;
    border-right: 1px solid rgba(100, 150, 100, 0.1);
    color: #aaaaaa;
    cursor: pointer;
    transition: all 0.2s ease;
    text-transform: uppercase;
    font-weight: 700;
    letter-spacing: 0.5px;
    font-size: 0.85rem;
}

.category-btn:hover {
    color: #ffffff;
    background: rgba(100, 150, 100, 0.1);
}

.category-btn.active {
    color: #64ff64;
    background: rgba(100, 150, 100, 0.15);
    border-bottom: 2px solid #64ff64;
    box-shadow: 0 0 10px rgba(100, 255, 100, 0.2);
}

/* Store Items Grid */
.store-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    padding: 20px 0;
}

/* Store Item Card - COD Style */
.store-item {
    background: linear-gradient(135deg, rgba(25, 30, 35, 0.95), rgba(15, 20, 25, 0.95));
    border: 1px solid rgba(100, 150, 100, 0.15);
    border-radius: 2px;
    padding: 0;
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.store-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(100, 255, 100, 0.1), transparent);
    transition: left 0.3s ease;
}

.store-item:hover::before {
    left: 100%;
}

.store-item:hover {
    border-color: var(--cod-primary-green);
    box-shadow: 0 0 20px rgba(100, 255, 100, 0.2);
    transform: translateY(-3px);
}

.store-item.featured {
    border-color: #ffff64;
    background: linear-gradient(135deg, rgba(40, 40, 20, 0.95), rgba(30, 30, 15, 0.95));
}

.store-item.featured::after {
    content: 'NEW';
    position: absolute;
    top: 10px;
    right: 10px;
    background: #ffff64;
    color: #000000;
    padding: 4px 8px;
    font-size: 0.7rem;
    font-weight: 700;
    border-radius: 2px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Store Item Image */
.store-item-image {
    width: 100%;
    height: 180px;
    object-fit: cover;
    filter: brightness(0.8) contrast(1.1);
    transition: filter 0.2s ease;
}

.store-item:hover .store-item-image {
    filter: brightness(1) contrast(1.2);
}

/* Store Item Content */
.store-item-content {
    padding: 15px;
}

.store-item-title {
    color: var(--cod-text-primary);
    font-size: 1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
}

.store-item-description {
    color: var(--cod-text-secondary);
    font-size: 0.85rem;
    margin-bottom: 12px;
    line-height: 1.4;
}

.store-item-price {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.price-current {
    color: var(--cod-primary-green);
    font-size: 1.1rem;
    font-weight: 700;
}

.price-original {
    color: var(--cod-text-muted);
    font-size: 0.9rem;
    text-decoration: line-through;
}

.store-item-actions {
    display: flex;
    gap: 8px;
}

.store-btn {
    flex: 1;
    padding: 8px 12px;
    background: linear-gradient(135deg, rgba(30, 35, 40, 0.9), rgba(20, 25, 30, 0.9));
    border: 1px solid var(--cod-border);
    color: var(--cod-text-primary);
    border-radius: 2px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.8rem;
}

.store-btn:hover {
    border-color: var(--cod-primary-green);
    color: var(--cod-primary-green);
    box-shadow: 0 0 10px rgba(100, 255, 100, 0.2);
}

.store-btn.primary {
    background: linear-gradient(135deg, var(--cod-secondary-green), var(--cod-primary-green));
    border-color: var(--cod-primary-green);
    color: #000000;
}

.store-btn.primary:hover {
    background: var(--cod-primary-green);
    box-shadow: 0 0 15px rgba(100, 255, 100, 0.4);
}

/* Store Item Rarity */
.store-item.common {
    border-left: 3px solid #888888;
}

.store-item.uncommon {
    border-left: 3px solid #64ff64;
}

.store-item.rare {
    border-left: 3px solid #4488ff;
}

.store-item.epic {
    border-left: 3px solid #aa44ff;
}

.store-item.legendary {
    border-left: 3px solid #ffaa44;
}

/* Store Filters */
.store-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    align-items: center;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-label {
    color: var(--cod-text-secondary);
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-select {
    background: rgba(0, 0, 0, 0.6);
    border: 1px solid var(--cod-border);
    border-radius: 2px;
    padding: 8px 12px;
    color: var(--cod-text-primary);
    font-size: 0.85rem;
    min-width: 120px;
}

.filter-select:focus {
    outline: none;
    border-color: var(--cod-primary-green);
    box-shadow: 0 0 10px rgba(100, 255, 100, 0.2);
}

/* Store Search */
.store-search {
    position: relative;
    max-width: 300px;
}

.store-search input {
    width: 100%;
    background: rgba(0, 0, 0, 0.6);
    border: 1px solid var(--cod-border);
    border-radius: 2px;
    padding: 10px 15px 10px 40px;
    color: var(--cod-text-primary);
    font-size: 0.9rem;
}

.store-search input:focus {
    outline: none;
    border-color: var(--cod-primary-green);
    box-shadow: 0 0 10px rgba(100, 255, 100, 0.2);
}

.store-search::before {
    content: '🔍';
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--cod-text-muted);
    font-size: 0.9rem;
}

/* Store Loading */
.store-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 60px 20px;
    color: var(--cod-text-secondary);
}

.store-loading .spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(100, 150, 100, 0.2);
    border-top: 3px solid var(--cod-primary-green);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Store Empty State */
.store-empty {
    text-align: center;
    padding: 60px 20px;
    color: var(--cod-text-muted);
}

.store-empty h3 {
    color: var(--cod-text-secondary);
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Store Pagination */
.store-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 30px;
    padding: 20px 0;
}

.pagination-btn {
    padding: 8px 12px;
    background: rgba(20, 25, 30, 0.8);
    border: 1px solid var(--cod-border);
    color: var(--cod-text-secondary);
    border-radius: 2px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 600;
}

.pagination-btn:hover {
    color: var(--cod-text-primary);
    border-color: var(--cod-primary-green);
}

.pagination-btn.active {
    background: var(--cod-primary-green);
    color: #000000;
    border-color: var(--cod-primary-green);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
