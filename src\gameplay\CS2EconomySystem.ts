import { EventEmitter } from 'events';
import { RoundSystem, RoundResult } from './RoundSystem';

export interface CS2WeaponPrice {
    id: string;
    name: string;
    category: 'pistol' | 'rifle' | 'smg' | 'sniper' | 'shotgun' | 'grenade' | 'equipment';
    price: number;
    team?: 'terrorist' | 'counter_terrorist' | 'both';
    killReward: number;
}

export interface PlayerEconomy {
    playerId: string;
    money: number;
    team: 'terrorist' | 'counter_terrorist';
    inventory: {
        primaryWeapon?: string;
        secondaryWeapon?: string;
        armor: boolean;
        helmet: boolean;
        defuseKit: boolean;
        grenades: string[];
    };
    roundStats: {
        kills: number;
        deaths: number;
        assists: number;
        bombPlants: number;
        bombDefuses: number;
        damage: number;
    };
}

export interface EconomyReward {
    playerId: string;
    amount: number;
    reason: 'kill' | 'assist' | 'bomb_plant' | 'bomb_defuse' | 'round_win' | 'round_loss' | 'loss_bonus';
    weaponUsed?: string;
}

export class CS2EconomySystem extends EventEmitter {
    private playerEconomies: Map<string, PlayerEconomy> = new Map();
    private weaponPrices: Map<string, CS2WeaponPrice> = new Map();
    private roundSystem: RoundSystem;
    private lossBonus: Map<'terrorist' | 'counter_terrorist', number> = new Map();
    private consecutiveLosses: Map<'terrorist' | 'counter_terrorist', number> = new Map();

    // Configurações econômicas do CS2
    private static readonly STARTING_MONEY = 800;
    private static readonly MAX_MONEY = 16000;
    private static readonly ROUND_WIN_REWARD = 3250;
    private static readonly ROUND_LOSS_BASE = 1400;
    private static readonly LOSS_BONUS_INCREMENT = 500;
    private static readonly MAX_LOSS_BONUS = 3400;
    private static readonly BOMB_PLANT_REWARD = 800;
    private static readonly BOMB_DEFUSE_REWARD = 300;
    private static readonly ASSIST_REWARD = 100;

    constructor(roundSystem: RoundSystem) {
        super();
        this.roundSystem = roundSystem;
        this.initializeWeaponPrices();
        this.initializeLossBonus();
        this.setupRoundSystemListeners();
        console.log('💰 Sistema de Economia CS2 inicializado');
    }

    private initializeWeaponPrices(): void {
        const weapons: CS2WeaponPrice[] = [
            // Pistolas
            { id: 'glock', name: 'Glock-18', category: 'pistol', price: 200, team: 'terrorist', killReward: 300 },
            { id: 'usp', name: 'USP-S', category: 'pistol', price: 200, team: 'counter_terrorist', killReward: 300 },
            { id: 'p250', name: 'P250', category: 'pistol', price: 300, team: 'both', killReward: 300 },
            { id: 'deagle', name: 'Desert Eagle', category: 'pistol', price: 700, team: 'both', killReward: 300 },

            // SMGs
            { id: 'mac10', name: 'MAC-10', category: 'smg', price: 1050, team: 'terrorist', killReward: 600 },
            { id: 'mp9', name: 'MP9', category: 'smg', price: 1250, team: 'counter_terrorist', killReward: 600 },
            { id: 'mp5', name: 'MP5-SD', category: 'smg', price: 1500, team: 'both', killReward: 600 },
            { id: 'p90', name: 'P90', category: 'smg', price: 2350, team: 'both', killReward: 300 },

            // Rifles
            { id: 'ak47', name: 'AK-47', category: 'rifle', price: 2700, team: 'terrorist', killReward: 300 },
            { id: 'm4a4', name: 'M4A4', category: 'rifle', price: 3100, team: 'counter_terrorist', killReward: 300 },
            { id: 'm4a1s', name: 'M4A1-S', category: 'rifle', price: 2900, team: 'counter_terrorist', killReward: 300 },
            { id: 'aug', name: 'AUG', category: 'rifle', price: 3300, team: 'counter_terrorist', killReward: 300 },
            { id: 'sg553', name: 'SG 553', category: 'rifle', price: 3000, team: 'terrorist', killReward: 300 },

            // Snipers
            { id: 'awp', name: 'AWP', category: 'sniper', price: 4750, team: 'both', killReward: 100 },
            { id: 'ssg08', name: 'SSG 08', category: 'sniper', price: 1700, team: 'both', killReward: 300 },

            // Shotguns
            { id: 'nova', name: 'Nova', category: 'shotgun', price: 1050, team: 'both', killReward: 900 },
            { id: 'mag7', name: 'MAG-7', category: 'shotgun', price: 1300, team: 'counter_terrorist', killReward: 900 },

            // Granadas
            { id: 'he_grenade', name: 'HE Grenade', category: 'grenade', price: 300, team: 'both', killReward: 300 },
            { id: 'flashbang', name: 'Flashbang', category: 'grenade', price: 200, team: 'both', killReward: 0 },
            { id: 'smoke', name: 'Smoke Grenade', category: 'grenade', price: 300, team: 'both', killReward: 0 },
            { id: 'molotov', name: 'Molotov', category: 'grenade', price: 400, team: 'terrorist', killReward: 300 },
            { id: 'incendiary', name: 'Incendiary', category: 'grenade', price: 600, team: 'counter_terrorist', killReward: 300 },

            // Equipamentos
            { id: 'kevlar', name: 'Kevlar Vest', category: 'equipment', price: 650, team: 'both', killReward: 0 },
            { id: 'kevlar_helmet', name: 'Kevlar + Helmet', category: 'equipment', price: 1000, team: 'both', killReward: 0 },
            { id: 'defuse_kit', name: 'Defuse Kit', category: 'equipment', price: 400, team: 'counter_terrorist', killReward: 0 }
        ];

        weapons.forEach(weapon => this.weaponPrices.set(weapon.id, weapon));
    }

    private initializeLossBonus(): void {
        this.lossBonus.set('terrorist', CS2EconomySystem.ROUND_LOSS_BASE);
        this.lossBonus.set('counter_terrorist', CS2EconomySystem.ROUND_LOSS_BASE);
        this.consecutiveLosses.set('terrorist', 0);
        this.consecutiveLosses.set('counter_terrorist', 0);
    }

    private setupRoundSystemListeners(): void {
        this.roundSystem.on('matchStarted', () => {
            this.resetAllPlayerEconomies();
        });

        this.roundSystem.on('roundEnded', (data) => {
            this.processRoundRewards(data);
        });

        this.roundSystem.on('bombPlanted', (data) => {
            this.addReward(data.playerId, CS2EconomySystem.BOMB_PLANT_REWARD, 'bomb_plant');
        });

        this.roundSystem.on('bombDefused', (data) => {
            this.addReward(data.playerId, CS2EconomySystem.BOMB_DEFUSE_REWARD, 'bomb_defuse');
        });
    }

    public createPlayerEconomy(playerId: string, team: 'terrorist' | 'counter_terrorist'): PlayerEconomy {
        const economy: PlayerEconomy = {
            playerId,
            money: CS2EconomySystem.STARTING_MONEY,
            team,
            inventory: {
                armor: false,
                helmet: false,
                defuseKit: false,
                grenades: []
            },
            roundStats: {
                kills: 0,
                deaths: 0,
                assists: 0,
                bombPlants: 0,
                bombDefuses: 0,
                damage: 0
            }
        };

        this.playerEconomies.set(playerId, economy);
        console.log(`💰 Economia criada para ${playerId} (${team}) - $${CS2EconomySystem.STARTING_MONEY}`);
        
        return economy;
    }

    public buyItem(playerId: string, itemId: string): boolean {
        const economy = this.playerEconomies.get(playerId);
        const weapon = this.weaponPrices.get(itemId);

        if (!economy || !weapon) {
            console.log(`❌ Jogador ou item não encontrado: ${playerId}, ${itemId}`);
            return false;
        }

        // Verifica se está no freeze time
        if (!this.roundSystem.isInFreezeTime()) {
            console.log(`❌ Compras só são permitidas durante o freeze time`);
            return false;
        }

        // Verifica se o time pode comprar este item
        if (weapon.team !== 'both' && weapon.team !== economy.team) {
            console.log(`❌ ${economy.team} não pode comprar ${weapon.name}`);
            return false;
        }

        // Verifica se tem dinheiro suficiente
        if (economy.money < weapon.price) {
            console.log(`❌ Dinheiro insuficiente: $${economy.money} < $${weapon.price}`);
            return false;
        }

        // Processa a compra
        economy.money -= weapon.price;
        this.addItemToInventory(economy, weapon);

        console.log(`✅ ${playerId} comprou ${weapon.name} por $${weapon.price} (restam $${economy.money})`);
        this.emit('itemPurchased', { playerId, item: weapon, remainingMoney: economy.money });

        return true;
    }

    private addItemToInventory(economy: PlayerEconomy, weapon: CS2WeaponPrice): void {
        switch (weapon.category) {
            case 'pistol':
                economy.inventory.secondaryWeapon = weapon.id;
                break;
            case 'rifle':
            case 'smg':
            case 'sniper':
            case 'shotgun':
                economy.inventory.primaryWeapon = weapon.id;
                break;
            case 'grenade':
                if (economy.inventory.grenades.length < 4) { // Limite de 4 granadas
                    economy.inventory.grenades.push(weapon.id);
                }
                break;
            case 'equipment':
                if (weapon.id === 'kevlar') {
                    economy.inventory.armor = true;
                } else if (weapon.id === 'kevlar_helmet') {
                    economy.inventory.armor = true;
                    economy.inventory.helmet = true;
                } else if (weapon.id === 'defuse_kit') {
                    economy.inventory.defuseKit = true;
                }
                break;
        }
    }

    public addKillReward(killerId: string, weaponUsed: string, victimId?: string): void {
        const weapon = this.weaponPrices.get(weaponUsed);
        const reward = weapon ? weapon.killReward : 300; // Reward padrão

        this.addReward(killerId, reward, 'kill', weaponUsed);

        // Atualiza estatísticas
        const economy = this.playerEconomies.get(killerId);
        if (economy) {
            economy.roundStats.kills++;
        }

        if (victimId) {
            const victimEconomy = this.playerEconomies.get(victimId);
            if (victimEconomy) {
                victimEconomy.roundStats.deaths++;
            }
        }
    }

    public addAssistReward(playerId: string): void {
        this.addReward(playerId, CS2EconomySystem.ASSIST_REWARD, 'assist');
        
        const economy = this.playerEconomies.get(playerId);
        if (economy) {
            economy.roundStats.assists++;
        }
    }

    private addReward(playerId: string, amount: number, reason: string, weaponUsed?: string): void {
        const economy = this.playerEconomies.get(playerId);
        if (!economy) return;

        const oldMoney = economy.money;
        economy.money = Math.min(economy.money + amount, CS2EconomySystem.MAX_MONEY);
        
        const actualReward = economy.money - oldMoney;

        console.log(`💰 ${playerId} recebeu $${actualReward} por ${reason} (total: $${economy.money})`);
        
        this.emit('rewardAdded', {
            playerId,
            amount: actualReward,
            reason,
            weaponUsed,
            totalMoney: economy.money
        } as EconomyReward);
    }

    private processRoundRewards(roundData: any): void {
        const winningTeam = roundData.winner;
        const losingTeam = winningTeam === 'terrorist' ? 'counter_terrorist' : 'terrorist';

        // Recompensas por vitória
        this.playerEconomies.forEach((economy) => {
            if (economy.team === winningTeam) {
                this.addReward(economy.playerId, CS2EconomySystem.ROUND_WIN_REWARD, 'round_win');
            } else {
                // Loss bonus para o time perdedor
                const lossBonus = this.lossBonus.get(losingTeam) || CS2EconomySystem.ROUND_LOSS_BASE;
                this.addReward(economy.playerId, lossBonus, 'round_loss');
            }
        });

        // Atualiza loss bonus
        this.updateLossBonus(winningTeam, losingTeam);

        // Reset do inventário para o próximo round
        this.resetInventories();
    }

    private updateLossBonus(winningTeam: 'terrorist' | 'counter_terrorist', losingTeam: 'terrorist' | 'counter_terrorist'): void {
        // Reset loss bonus do time vencedor
        this.consecutiveLosses.set(winningTeam, 0);
        this.lossBonus.set(winningTeam, CS2EconomySystem.ROUND_LOSS_BASE);

        // Incrementa loss bonus do time perdedor
        const currentLosses = this.consecutiveLosses.get(losingTeam) || 0;
        this.consecutiveLosses.set(losingTeam, currentLosses + 1);

        const newLossBonus = Math.min(
            CS2EconomySystem.ROUND_LOSS_BASE + (currentLosses * CS2EconomySystem.LOSS_BONUS_INCREMENT),
            CS2EconomySystem.MAX_LOSS_BONUS
        );
        this.lossBonus.set(losingTeam, newLossBonus);

        console.log(`📊 Loss bonus atualizado - ${losingTeam}: $${newLossBonus} (${currentLosses + 1} derrotas consecutivas)`);
    }

    private resetInventories(): void {
        this.playerEconomies.forEach((economy) => {
            // Mantém apenas armor/helmet se não foram danificados
            // Em um jogo real, isso dependeria do dano recebido
            economy.inventory.primaryWeapon = undefined;
            economy.inventory.secondaryWeapon = undefined;
            economy.inventory.grenades = [];
            economy.inventory.defuseKit = false;
            
            // Reset das estatísticas do round
            economy.roundStats = {
                kills: 0,
                deaths: 0,
                assists: 0,
                bombPlants: 0,
                bombDefuses: 0,
                damage: 0
            };
        });
    }

    private resetAllPlayerEconomies(): void {
        this.playerEconomies.forEach((economy) => {
            economy.money = CS2EconomySystem.STARTING_MONEY;
            economy.inventory = {
                armor: false,
                helmet: false,
                defuseKit: false,
                grenades: []
            };
        });

        this.initializeLossBonus();
        console.log('🔄 Todas as economias dos jogadores foram resetadas');
    }

    // Getters públicos
    public getPlayerEconomy(playerId: string): PlayerEconomy | undefined {
        return this.playerEconomies.get(playerId);
    }

    public getWeaponPrice(weaponId: string): CS2WeaponPrice | undefined {
        return this.weaponPrices.get(weaponId);
    }

    public getWeaponsByCategory(category: string, team?: 'terrorist' | 'counter_terrorist'): CS2WeaponPrice[] {
        return Array.from(this.weaponPrices.values()).filter(weapon => {
            const categoryMatch = weapon.category === category;
            const teamMatch = !team || weapon.team === 'both' || weapon.team === team;
            return categoryMatch && teamMatch;
        });
    }

    public getAllWeapons(): CS2WeaponPrice[] {
        return Array.from(this.weaponPrices.values());
    }

    public getTeamLossBonus(team: 'terrorist' | 'counter_terrorist'): number {
        return this.lossBonus.get(team) || CS2EconomySystem.ROUND_LOSS_BASE;
    }
}
