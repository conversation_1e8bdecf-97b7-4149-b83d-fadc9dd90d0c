using UnrealBuildTool;
using System.Collections.Generic;

public class TacticalNexusUE5Target : TargetRules
{
	public TacticalNexusUE5Target(TargetInfo Target) : base(Target)
	{
		Type = TargetType.Game;
		DefaultBuildSettings = BuildSettingsVersion.V2;
		ExtraModuleNames.AddRange(new string[] { "TacticalNexusUE5" });
		
		// Performance optimizations
		bUseUnityBuild = true;
		bUsePCHFiles = true;
		bUseSharedPCHs = true;
		
		// Steam integration
		if (Target.Platform == UnrealTargetPlatform.Win64)
		{
			GlobalDefinitions.Add("STEAM_ENABLED=1");
		}

		// Enable modern C++ features
		CppStandard = CppStandardVersion.Cpp17;
		
		// Optimization settings
		bCompileWithStatsWithoutEngine = true;
		bCompileWithPluginSupport = true;
	}
}
