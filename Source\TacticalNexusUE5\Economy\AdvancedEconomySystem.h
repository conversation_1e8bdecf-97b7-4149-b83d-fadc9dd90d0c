// Copyright Tactical Nexus Team. All Rights Reserved.
// REVOLUTIONARY ECONOMY & PROGRESSION SYSTEM - BEYOND CS2

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Engine/DataTable.h"
#include "Net/UnrealNetwork.h"
#include "AdvancedEconomySystem.generated.h"

UENUM(BlueprintType)
enum class ECurrencyType : uint8
{
    Credits             UMETA(DisplayName = "Credits"),
    PremiumCoins        UMETA(DisplayName = "Premium Coins"),
    BattleTokens        UMETA(DisplayName = "Battle Tokens"),
    SkillPoints         UMETA(DisplayName = "Skill Points"),
    Reputation          UMETA(DisplayName = "Reputation"),
    ExperiencePoints    UMETA(DisplayName = "Experience Points")
};

UENUM(BlueprintType)
enum class EItemRarity : uint8
{
    Common              UMETA(DisplayName = "Common"),
    Uncommon            UMETA(DisplayName = "Uncommon"),
    Rare                UMETA(DisplayName = "Rare"),
    Epic                UMETA(DisplayName = "Epic"),
    Legendary           UMETA(DisplayName = "Legendary"),
    Mythical            UMETA(DisplayName = "Mythical"),
    Immortal            UMETA(DisplayName = "Immortal"),
    Transcendent        UMETA(DisplayName = "Transcendent")
};

UENUM(BlueprintType)
enum class EItemType : uint8
{
    Weapon              UMETA(DisplayName = "Weapon"),
    WeaponSkin          UMETA(DisplayName = "Weapon Skin"),
    CharacterSkin       UMETA(DisplayName = "Character Skin"),
    Gloves              UMETA(DisplayName = "Gloves"),
    Knife               UMETA(DisplayName = "Knife"),
    Sticker             UMETA(DisplayName = "Sticker"),
    Charm               UMETA(DisplayName = "Charm"),
    Emote               UMETA(DisplayName = "Emote"),
    VoiceLine           UMETA(DisplayName = "Voice Line"),
    MusicKit            UMETA(DisplayName = "Music Kit"),
    Graffiti            UMETA(DisplayName = "Graffiti"),
    Badge               UMETA(DisplayName = "Badge"),
    Title               UMETA(DisplayName = "Title"),
    Effect              UMETA(DisplayName = "Effect")
};

USTRUCT(BlueprintType)
struct FAdvancedItem
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
    FString ItemID;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
    FString ItemName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
    FString Description;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
    EItemType ItemType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
    EItemRarity Rarity;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
    TMap<ECurrencyType, int32> Price;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
    bool bIsTradeable;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
    bool bIsMarketable;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
    float WearValue; // 0-1 for weapon skins

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
    TArray<FString> StatTrakCounters;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
    TArray<FString> Stickers;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
    FDateTime AcquiredDate;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
    int32 MarketValue; // Dynamic market price

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
    float DropRate; // Probability of dropping

    FAdvancedItem()
    {
        ItemID = TEXT("");
        ItemName = TEXT("");
        Description = TEXT("");
        ItemType = EItemType::Weapon;
        Rarity = EItemRarity::Common;
        bIsTradeable = true;
        bIsMarketable = true;
        WearValue = 0.0f;
        AcquiredDate = FDateTime::Now();
        MarketValue = 0;
        DropRate = 0.1f;
    }
};

USTRUCT(BlueprintType)
struct FPlayerWallet
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wallet")
    TMap<ECurrencyType, int32> Currencies;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wallet")
    TArray<FAdvancedItem> Inventory;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wallet")
    TArray<FString> EquippedItems;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wallet")
    int32 InventorySlots;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wallet")
    float TotalPortfolioValue;

    FPlayerWallet()
    {
        // Initialize starting currencies
        Currencies.Add(ECurrencyType::Credits, 800); // CS2 starting money
        Currencies.Add(ECurrencyType::PremiumCoins, 0);
        Currencies.Add(ECurrencyType::BattleTokens, 0);
        Currencies.Add(ECurrencyType::SkillPoints, 0);
        Currencies.Add(ECurrencyType::Reputation, 1000);
        Currencies.Add(ECurrencyType::ExperiencePoints, 0);
        
        InventorySlots = 100;
        TotalPortfolioValue = 0.0f;
    }
};

USTRUCT(BlueprintType)
struct FMarketTransaction
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Transaction")
    FString TransactionID;

    UPROPERTY(BlueprintReadOnly, Category = "Transaction")
    FString SellerID;

    UPROPERTY(BlueprintReadOnly, Category = "Transaction")
    FString BuyerID;

    UPROPERTY(BlueprintReadOnly, Category = "Transaction")
    FAdvancedItem Item;

    UPROPERTY(BlueprintReadOnly, Category = "Transaction")
    int32 Price;

    UPROPERTY(BlueprintReadOnly, Category = "Transaction")
    ECurrencyType Currency;

    UPROPERTY(BlueprintReadOnly, Category = "Transaction")
    FDateTime TransactionDate;

    UPROPERTY(BlueprintReadOnly, Category = "Transaction")
    bool bCompleted;

    FMarketTransaction()
    {
        TransactionID = TEXT("");
        SellerID = TEXT("");
        BuyerID = TEXT("");
        Price = 0;
        Currency = ECurrencyType::Credits;
        TransactionDate = FDateTime::Now();
        bCompleted = false;
    }
};

USTRUCT(BlueprintType)
struct FBattlePass
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Battle Pass")
    FString SeasonName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Battle Pass")
    int32 CurrentTier;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Battle Pass")
    int32 MaxTiers;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Battle Pass")
    int32 CurrentXP;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Battle Pass")
    int32 XPToNextTier;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Battle Pass")
    bool bPremiumPass;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Battle Pass")
    TArray<FAdvancedItem> FreeRewards;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Battle Pass")
    TArray<FAdvancedItem> PremiumRewards;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Battle Pass")
    TArray<bool> ClaimedRewards;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Battle Pass")
    FDateTime SeasonEndDate;

    FBattlePass()
    {
        SeasonName = TEXT("Season 1");
        CurrentTier = 1;
        MaxTiers = 100;
        CurrentXP = 0;
        XPToNextTier = 1000;
        bPremiumPass = false;
        SeasonEndDate = FDateTime::Now() + FTimespan(90, 0, 0, 0); // 90 days
    }
};

USTRUCT(BlueprintType)
struct FCryptoIntegration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Crypto")
    bool bNFTSupport;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Crypto")
    bool bBlockchainVerification;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Crypto")
    FString WalletAddress;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Crypto")
    TArray<FString> OwnedNFTs;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Crypto")
    float CryptoBalance;

    FCryptoIntegration()
    {
        bNFTSupport = false;
        bBlockchainVerification = false;
        WalletAddress = TEXT("");
        CryptoBalance = 0.0f;
    }
};

UCLASS(Blueprintable, BlueprintType, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class TACTICALNEXUSUE5_API UAdvancedEconomySystem : public UActorComponent
{
    GENERATED_BODY()

public:
    UAdvancedEconomySystem();

protected:
    virtual void BeginPlay() override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // Currency Management
    UFUNCTION(BlueprintCallable, Category = "Economy")
    bool AddCurrency(ECurrencyType CurrencyType, int32 Amount);

    UFUNCTION(BlueprintCallable, Category = "Economy")
    bool SpendCurrency(ECurrencyType CurrencyType, int32 Amount);

    UFUNCTION(BlueprintCallable, Category = "Economy")
    int32 GetCurrencyAmount(ECurrencyType CurrencyType);

    UFUNCTION(BlueprintCallable, Category = "Economy")
    void ConvertCurrency(ECurrencyType FromCurrency, ECurrencyType ToCurrency, int32 Amount);

    // Item Management
    UFUNCTION(BlueprintCallable, Category = "Items")
    bool AddItemToInventory(const FAdvancedItem& Item);

    UFUNCTION(BlueprintCallable, Category = "Items")
    bool RemoveItemFromInventory(const FString& ItemID);

    UFUNCTION(BlueprintCallable, Category = "Items")
    FAdvancedItem GetItemByID(const FString& ItemID);

    UFUNCTION(BlueprintCallable, Category = "Items")
    TArray<FAdvancedItem> GetItemsByType(EItemType ItemType);

    UFUNCTION(BlueprintCallable, Category = "Items")
    TArray<FAdvancedItem> GetItemsByRarity(EItemRarity Rarity);

    // Market System
    UFUNCTION(BlueprintCallable, Category = "Market")
    bool ListItemOnMarket(const FString& ItemID, int32 Price, ECurrencyType Currency);

    UFUNCTION(BlueprintCallable, Category = "Market")
    bool BuyItemFromMarket(const FString& TransactionID);

    UFUNCTION(BlueprintCallable, Category = "Market")
    TArray<FMarketTransaction> GetMarketListings(EItemType ItemType = EItemType::Weapon);

    UFUNCTION(BlueprintCallable, Category = "Market")
    void UpdateMarketPrices();

    // Trading System
    UFUNCTION(BlueprintCallable, Category = "Trading")
    bool InitiateTrade(const FString& TargetPlayerID, const TArray<FString>& OfferedItems, const TArray<FString>& RequestedItems);

    UFUNCTION(BlueprintCallable, Category = "Trading")
    bool AcceptTrade(const FString& TradeID);

    UFUNCTION(BlueprintCallable, Category = "Trading")
    bool DeclineTrade(const FString& TradeID);

    // Drop System
    UFUNCTION(BlueprintCallable, Category = "Drops")
    FAdvancedItem GenerateRandomDrop(EItemType ItemType = EItemType::WeaponSkin);

    UFUNCTION(BlueprintCallable, Category = "Drops")
    void ProcessMatchEndDrops(bool bWon, float MatchDuration, int32 Score);

    UFUNCTION(BlueprintCallable, Category = "Drops")
    void OpenCase(const FString& CaseID);

    // Battle Pass
    UFUNCTION(BlueprintCallable, Category = "Battle Pass")
    void AddBattlePassXP(int32 XP);

    UFUNCTION(BlueprintCallable, Category = "Battle Pass")
    bool ClaimBattlePassReward(int32 Tier, bool bPremium);

    UFUNCTION(BlueprintCallable, Category = "Battle Pass")
    bool UpgradeToPremiumPass();

    // Achievements & Challenges
    UFUNCTION(BlueprintCallable, Category = "Achievements")
    void UpdateAchievementProgress(const FString& AchievementID, int32 Progress);

    UFUNCTION(BlueprintCallable, Category = "Achievements")
    void CompleteDaily Challenge(const FString& ChallengeID);

    UFUNCTION(BlueprintCallable, Category = "Achievements")
    TArray<FString> GetAvailableChallenges();

    // Crypto Integration
    UFUNCTION(BlueprintCallable, Category = "Crypto")
    bool ConnectCryptoWallet(const FString& WalletAddress);

    UFUNCTION(BlueprintCallable, Category = "Crypto")
    bool MintItemAsNFT(const FString& ItemID);

    UFUNCTION(BlueprintCallable, Category = "Crypto")
    bool VerifyNFTOwnership(const FString& NFTID);

    // Analytics
    UFUNCTION(BlueprintCallable, Category = "Analytics")
    void TrackPurchase(const FString& ItemID, int32 Price, ECurrencyType Currency);

    UFUNCTION(BlueprintCallable, Category = "Analytics")
    void TrackItemUsage(const FString& ItemID, float UsageTime);

    UFUNCTION(BlueprintCallable, Category = "Analytics")
    FString GenerateEconomyReport();

protected:
    // Player Economy Data
    UPROPERTY(Replicated, BlueprintReadOnly, Category = "Economy")
    FPlayerWallet PlayerWallet;

    UPROPERTY(Replicated, BlueprintReadOnly, Category = "Battle Pass")
    FBattlePass CurrentBattlePass;

    UPROPERTY(BlueprintReadOnly, Category = "Crypto")
    FCryptoIntegration CryptoData;

    // Market Data
    UPROPERTY(BlueprintReadOnly, Category = "Market")
    TArray<FMarketTransaction> ActiveListings;

    UPROPERTY(BlueprintReadOnly, Category = "Market")
    TMap<FString, float> MarketPriceHistory;

    // Item Database
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Database")
    UDataTable* ItemDatabase;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Database")
    TMap<EItemRarity, float> RarityDropRates;

    // Economy Settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
    float MarketTaxRate;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
    int32 MaxMarketListings;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
    bool bEnableTrading;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
    bool bEnableCryptoIntegration;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
    float DailyDropCooldown;

private:
    // Internal systems
    FDateTime LastDropTime;
    TMap<FString, FDateTime> CooldownTimers;
    
    // Market algorithms
    void CalculateDynamicPricing();
    void ProcessMarketTrends();
    void DetectMarketManipulation();
    
    // Drop algorithms
    EItemRarity DetermineDropRarity();
    FAdvancedItem GenerateItemWithRarity(EItemRarity Rarity, EItemType ItemType);
    
    // Blockchain integration
    void SyncWithBlockchain();
    bool ValidateNFTTransaction(const FString& TransactionHash);
};
