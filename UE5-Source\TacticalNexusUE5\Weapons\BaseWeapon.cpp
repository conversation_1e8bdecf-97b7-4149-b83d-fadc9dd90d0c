// Copyright Tactical Nexus Team. All Rights Reserved.

#include "BaseWeapon.h"
#include "Components/SkeletalMeshComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "DrawDebugHelpers.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetMathLibrary.h"
#include "Sound/SoundCue.h"
#include "Particles/ParticleSystemComponent.h"
#include "Net/UnrealNetwork.h"
#include "TimerManager.h"
#include "../Characters/TacticalNexusCharacter.h"

ABaseWeapon::ABaseWeapon()
{
    PrimaryActorTick.bCanEverTick = true;
    bReplicates = true;
    bNetUseOwnerRelevancy = true;

    // Create weapon mesh component
    WeaponMesh = CreateDefaultSubobject<USkeletalMeshComponent>(TEXT("WeaponMesh"));
    RootComponent = WeaponMesh;
    WeaponMesh->SetCollisionEnabled(ECollisionEnabled::NoCollision);
    WeaponMesh->SetCollisionResponseToAllChannels(ECR_Ignore);

    // Create muzzle flash component
    MuzzleFlashComponent = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("MuzzleFlashComponent"));
    MuzzleFlashComponent->SetupAttachment(WeaponMesh, TEXT("MuzzleFlashSocket"));
    MuzzleFlashComponent->SetCollisionEnabled(ECollisionEnabled::NoCollision);

    // Initialize weapon state
    CurrentAmmo = WeaponStats.MagazineSize;
    ReserveAmmo = WeaponStats.MagazineSize * 3; // 3 extra magazines
    bIsReloading = false;
    bIsFiring = false;
    CurrentRecoilIndex = 0;
    LastFireTime = 0.0f;
    CurrentRecoilOffset = FVector2D::ZeroVector;
    TimeBetweenShots = 60.0f / WeaponStats.FireRate;
    LastShotTime = 0.0f;
    WeaponID = 0;
}

void ABaseWeapon::BeginPlay()
{
    Super::BeginPlay();
    
    // Cache owner character
    OwnerCharacter = Cast<ATacticalNexusCharacter>(GetOwner());
    
    // Initialize weapon stats based on type
    InitializeWeaponStats();
}

void ABaseWeapon::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(ABaseWeapon, CurrentAmmo);
    DOREPLIFETIME(ABaseWeapon, ReserveAmmo);
    DOREPLIFETIME(ABaseWeapon, bIsReloading);
    DOREPLIFETIME(ABaseWeapon, bIsFiring);
    DOREPLIFETIME(ABaseWeapon, WeaponID);
}

void ABaseWeapon::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Update recoil recovery
    if (!bIsFiring && CurrentRecoilOffset.Size() > 0.01f)
    {
        CurrentRecoilOffset = FMath::VInterpTo(
            FVector(CurrentRecoilOffset.X, CurrentRecoilOffset.Y, 0.0f),
            FVector::ZeroVector,
            DeltaTime,
            RecoilPattern.RecoveryRate
        );
    }
}

void ABaseWeapon::StartFire()
{
    if (!CanFire())
    {
        return;
    }

    bIsFiring = true;

    if (HasAuthority())
    {
        ServerFire();
    }
    else
    {
        ServerFire();
    }

    // Handle automatic fire
    if (WeaponStats.bIsAutomatic)
    {
        GetWorldTimerManager().SetTimer(
            FireTimerHandle,
            this,
            &ABaseWeapon::StartFire,
            TimeBetweenShots,
            true
        );
    }
}

void ABaseWeapon::StopFire()
{
    bIsFiring = false;
    GetWorldTimerManager().ClearTimer(FireTimerHandle);
}

void ABaseWeapon::Reload()
{
    if (!CanReload())
    {
        return;
    }

    if (HasAuthority())
    {
        ServerReload();
    }
    else
    {
        ServerReload();
    }
}

bool ABaseWeapon::CanFire() const
{
    return !bIsReloading && 
           CurrentAmmo > 0 && 
           (GetWorld()->GetTimeSeconds() - LastShotTime) >= TimeBetweenShots;
}

bool ABaseWeapon::CanReload() const
{
    return !bIsReloading && 
           CurrentAmmo < WeaponStats.MagazineSize && 
           ReserveAmmo > 0;
}

void ABaseWeapon::OnRep_CurrentAmmo()
{
    // Update UI or other client-side effects
    if (OwnerCharacter && OwnerCharacter->IsLocallyControlled())
    {
        // Update ammo display
    }
}

void ABaseWeapon::OnRep_IsReloading()
{
    if (bIsReloading)
    {
        PlayReloadEffects();
    }
}

bool ABaseWeapon::ServerFire_Validate()
{
    return true;
}

void ABaseWeapon::ServerFire_Implementation()
{
    if (CanFire())
    {
        Fire();
    }
}

bool ABaseWeapon::ServerReload_Validate()
{
    return true;
}

void ABaseWeapon::ServerReload_Implementation()
{
    if (CanReload())
    {
        bIsReloading = true;
        
        GetWorldTimerManager().SetTimer(
            ReloadTimerHandle,
            [this]()
            {
                // Calculate ammo to reload
                int32 AmmoToReload = FMath::Min(
                    WeaponStats.MagazineSize - CurrentAmmo,
                    ReserveAmmo
                );
                
                CurrentAmmo += AmmoToReload;
                ReserveAmmo -= AmmoToReload;
                bIsReloading = false;
            },
            WeaponStats.ReloadTime,
            false
        );

        MulticastPlayReloadEffects();
    }
}

void ABaseWeapon::MulticastPlayFireEffects_Implementation()
{
    PlayFireEffects();
}

void ABaseWeapon::MulticastPlayReloadEffects_Implementation()
{
    PlayReloadEffects();
}

void ABaseWeapon::Fire()
{
    if (!CanFire())
    {
        return;
    }

    // Consume ammo
    CurrentAmmo--;
    LastShotTime = GetWorld()->GetTimeSeconds();

    // Perform line trace
    FVector StartLocation;
    FVector EndLocation;
    FRotator FireRotation;

    if (OwnerCharacter)
    {
        FVector CameraLocation;
        FRotator CameraRotation;
        OwnerCharacter->GetActorEyesViewPoint(CameraLocation, CameraRotation);

        // Apply recoil and spread
        FVector RecoilOffset = CalculateRecoil();
        FVector SpreadOffset = CalculateSpread();
        
        FireRotation = CameraRotation + FRotator(
            RecoilOffset.Y + SpreadOffset.Y,
            RecoilOffset.X + SpreadOffset.X,
            0.0f
        );

        StartLocation = CameraLocation;
        EndLocation = StartLocation + (FireRotation.Vector() * WeaponStats.Range);
    }

    // Perform line trace
    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.AddIgnoredActor(this);
    QueryParams.AddIgnoredActor(GetOwner());
    QueryParams.bTraceComplex = true;

    bool bHit = GetWorld()->LineTraceSingleByChannel(
        HitResult,
        StartLocation,
        EndLocation,
        ECC_Visibility,
        QueryParams
    );

    if (bHit)
    {
        ProcessHit(HitResult);
    }

    // Play effects
    MulticastPlayFireEffects();

    // Update recoil
    if (CurrentRecoilIndex < RecoilPattern.RecoilPoints.Num())
    {
        CurrentRecoilOffset += RecoilPattern.RecoilPoints[CurrentRecoilIndex] * RecoilPattern.RecoilMultiplier;
        CurrentRecoilIndex++;
    }
}

void ABaseWeapon::ProcessHit(const FHitResult& HitResult)
{
    // Apply damage if hit an actor
    if (HitResult.GetActor())
    {
        // TODO: Apply damage based on hit location (headshot, body, etc.)
        float DamageAmount = WeaponStats.Damage;
        
        // Check for headshot
        if (HitResult.BoneName == TEXT("head") || HitResult.BoneName == TEXT("Head"))
        {
            DamageAmount *= 4.0f; // CS2-style headshot multiplier
        }

        UGameplayStatics::ApplyPointDamage(
            HitResult.GetActor(),
            DamageAmount,
            HitResult.Location,
            HitResult,
            OwnerCharacter ? OwnerCharacter->GetController() : nullptr,
            this,
            UDamageType::StaticClass()
        );
    }

    // Spawn impact effects
    if (ImpactVFX)
    {
        UGameplayStatics::SpawnEmitterAtLocation(
            GetWorld(),
            ImpactVFX,
            HitResult.Location,
            HitResult.Normal.Rotation()
        );
    }
}

void ABaseWeapon::PlayFireEffects()
{
    // Play fire sound
    if (FireSound)
    {
        UGameplayStatics::PlaySoundAtLocation(
            GetWorld(),
            FireSound,
            GetActorLocation()
        );
    }

    // Play muzzle flash VFX
    if (MuzzleFlashVFX && WeaponMesh)
    {
        UGameplayStatics::SpawnEmitterAttached(
            MuzzleFlashVFX,
            WeaponMesh,
            TEXT("MuzzleFlashSocket"),
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::SnapToTarget,
            true
        );
    }
}

void ABaseWeapon::PlayReloadEffects()
{
    // Play reload sound
    if (ReloadSound)
    {
        UGameplayStatics::PlaySoundAtLocation(
            GetWorld(),
            ReloadSound,
            GetActorLocation()
        );
    }
}

FVector ABaseWeapon::CalculateRecoil()
{
    // Return current recoil offset
    return FVector(CurrentRecoilOffset.X, CurrentRecoilOffset.Y, 0.0f);
}

FVector ABaseWeapon::CalculateSpread()
{
    // Calculate spread based on movement, crouching, etc.
    float SpreadAmount = (1.0f - WeaponStats.Accuracy) * 2.0f;
    
    // Modify spread based on player state
    if (OwnerCharacter)
    {
        // Increase spread when moving
        if (OwnerCharacter->GetVelocity().Size() > 10.0f)
        {
            SpreadAmount *= 2.0f;
        }
        
        // Decrease spread when crouching
        if (OwnerCharacter->bIsCrouched)
        {
            SpreadAmount *= 0.5f;
        }
    }

    return FVector(
        FMath::RandRange(-SpreadAmount, SpreadAmount),
        FMath::RandRange(-SpreadAmount, SpreadAmount),
        0.0f
    );
}

void ABaseWeapon::InitializeWeaponStats()
{
    // This would typically load from a data table
    // For now, set default values based on weapon type
    switch (WeaponStats.WeaponType)
    {
        case EWeaponType::Rifle:
            WeaponStats.Damage = 30;
            WeaponStats.FireRate = 600;
            WeaponStats.MagazineSize = 30;
            WeaponStats.Price = 2700;
            break;
        case EWeaponType::Pistol:
            WeaponStats.Damage = 25;
            WeaponStats.FireRate = 400;
            WeaponStats.MagazineSize = 12;
            WeaponStats.Price = 500;
            break;
        case EWeaponType::Sniper:
            WeaponStats.Damage = 115;
            WeaponStats.FireRate = 40;
            WeaponStats.MagazineSize = 10;
            WeaponStats.Price = 4750;
            break;
        default:
            break;
    }
    
    // Update derived values
    TimeBetweenShots = 60.0f / WeaponStats.FireRate;
    CurrentAmmo = WeaponStats.MagazineSize;
}
