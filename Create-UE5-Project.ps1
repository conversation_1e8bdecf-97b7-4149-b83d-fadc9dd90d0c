# Tactical Nexus UE5 Project Creator
# Copyright Tactical Nexus Team. All Rights Reserved.

Write-Host "🎮 TACTICAL NEXUS UE5 PROJECT CREATOR" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan

$ProjectPath = "C:\TacticalNexusUE5"
$ProjectName = "TacticalNexusUE5"

# Create project directory
Write-Host "📁 Criando diretório do projeto..." -ForegroundColor Yellow
if (-not (Test-Path $ProjectPath)) {
    New-Item -ItemType Directory -Path $ProjectPath -Force | Out-Null
}

# Create basic directory structure
$Directories = @(
    "Content\Core",
    "Content\Weapons", 
    "Content\Maps",
    "Content\UI",
    "Content\Audio",
    "Content\VFX",
    "Source\TacticalNexusUE5",
    "Config"
)

foreach ($Dir in $Directories) {
    $FullPath = Join-Path $ProjectPath $Dir
    if (-not (Test-Path $FullPath)) {
        New-Item -ItemType Directory -Path $FullPath -Force | Out-Null
    }
}

Write-Host "✅ Estrutura de diretórios criada" -ForegroundColor Green

# Create .uproject file
$ProjectFile = "$ProjectPath\$ProjectName.uproject"
$UProjectJson = @"
{
    "FileVersion": 3,
    "EngineAssociation": "5.4",
    "Category": "",
    "Description": "Tactical Nexus - Counter-Strike 2 Clone in Unreal Engine 5",
    "Modules": [
        {
            "Name": "TacticalNexusUE5",
            "Type": "Runtime",
            "LoadingPhase": "Default"
        }
    ],
    "Plugins": [
        {
            "Name": "OnlineSubsystemSteam",
            "Enabled": true
        },
        {
            "Name": "Niagara",
            "Enabled": true
        }
    ],
    "TargetPlatforms": [
        "Windows"
    ]
}
"@

$UProjectJson | Out-File -FilePath $ProjectFile -Encoding UTF8
Write-Host "✅ Arquivo .uproject criado" -ForegroundColor Green

# Create Build.cs file
$BuildCSPath = "$ProjectPath\Source\TacticalNexusUE5\TacticalNexusUE5.Build.cs"
$BuildCSContent = @"
using UnrealBuildTool;

public class TacticalNexusUE5 : ModuleRules
{
    public TacticalNexusUE5(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicDependencyModuleNames.AddRange(new string[] { 
            "Core", 
            "CoreUObject", 
            "Engine", 
            "InputCore",
            "OnlineSubsystem",
            "OnlineSubsystemSteam",
            "Niagara"
        });
    }
}
"@

$BuildCSContent | Out-File -FilePath $BuildCSPath -Encoding UTF8

# Create Target.cs file
$TargetCSPath = "$ProjectPath\Source\TacticalNexusUE5.Target.cs"
$TargetCSContent = @"
using UnrealBuildTool;
using System.Collections.Generic;

public class TacticalNexusUE5Target : TargetRules
{
    public TacticalNexusUE5Target(TargetInfo Target) : base(Target)
    {
        Type = TargetType.Game;
        DefaultBuildSettings = BuildSettingsVersion.V2;
        ExtraModuleNames.AddRange(new string[] { "TacticalNexusUE5" });
    }
}
"@

$TargetCSContent | Out-File -FilePath $TargetCSPath -Encoding UTF8

Write-Host "✅ Arquivos de build criados" -ForegroundColor Green

# Copy source files if they exist
$CurrentDir = Get-Location
$SourceFiles = @(
    "UE5-Source\TacticalNexusUE5\Weapons\BaseWeapon.h",
    "UE5-Source\TacticalNexusUE5\Weapons\BaseWeapon.cpp",
    "UE5-Source\TacticalNexusUE5\Core\TacticalNexusGameMode.h"
)

foreach ($SourceFile in $SourceFiles) {
    $SourcePath = Join-Path $CurrentDir $SourceFile
    if (Test-Path $SourcePath) {
        $DestDir = Split-Path $SourceFile -Parent
        $DestDir = $DestDir -replace "UE5-Source", "Source"
        $DestPath = Join-Path $ProjectPath $DestDir
        
        if (-not (Test-Path $DestPath)) {
            New-Item -ItemType Directory -Path $DestPath -Force | Out-Null
        }
        
        $FileName = Split-Path $SourceFile -Leaf
        $FinalDestPath = Join-Path $DestPath $FileName
        Copy-Item $SourcePath $FinalDestPath -Force
        Write-Host "✅ Copiado: $FileName" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "🎉 PROJETO UE5 CRIADO COM SUCESSO!" -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Green
Write-Host ""
Write-Host "📍 Localização: $ProjectPath" -ForegroundColor Cyan
Write-Host "🎮 Arquivo do projeto: $ProjectFile" -ForegroundColor Cyan
Write-Host ""
Write-Host "🚀 PRÓXIMOS PASSOS:" -ForegroundColor Yellow
Write-Host "1. Instalar Unreal Engine 5.4+ se necessário" -ForegroundColor White
Write-Host "2. Duplo-clique no arquivo .uproject para abrir" -ForegroundColor White
Write-Host "3. Compilar o projeto quando solicitado" -ForegroundColor White
Write-Host "4. Começar a implementar os sistemas de jogo" -ForegroundColor White
Write-Host ""
Write-Host "🎯 READY TO START UE5 DEVELOPMENT! 🎯" -ForegroundColor Green

# Try to open the project if UE5 is installed
$UE5Paths = @(
    "C:\Program Files\Epic Games\UE_5.4\Engine\Binaries\Win64\UnrealEditor.exe",
    "C:\Program Files\Epic Games\UE_5.3\Engine\Binaries\Win64\UnrealEditor.exe",
    "C:\Program Files\Epic Games\UE_5.2\Engine\Binaries\Win64\UnrealEditor.exe"
)

foreach ($UE5Path in $UE5Paths) {
    if (Test-Path $UE5Path) {
        Write-Host ""
        Write-Host "🚀 Abrindo projeto no Unreal Engine 5..." -ForegroundColor Green
        Start-Process -FilePath $UE5Path -ArgumentList "`"$ProjectFile`""
        break
    }
}

Write-Host ""
Write-Host "📝 NOTA: Se o UE5 não abrir automaticamente," -ForegroundColor Yellow
Write-Host "   instale o Unreal Engine 5.4+ pelo Epic Games Launcher" -ForegroundColor Yellow
