{"FileVersion": 3, "EngineAssociation": "5.4", "Category": "", "Description": "Tactical Nexus - Counter-Strike 2 Clone in Unreal Engine 5", "Modules": [{"Name": "TacticalNexusUE5", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "AdditionalDependencies": ["Engine", "CoreUObject", "OnlineSubsystemSteam", "Niagara", "AudioMixer"]}], "Plugins": [{"Name": "OnlineSubsystemSteam", "Enabled": true}, {"Name": "Niagara", "Enabled": true}, {"Name": "ReplicationGraph", "Enabled": true}, {"Name": "DLSS", "Enabled": true}, {"Name": "FSR", "Enabled": true}, {"Name": "<PERSON><PERSON>", "Enabled": true}, {"Name": "Nanite", "Enabled": true}], "TargetPlatforms": ["Windows"], "EpicSampleNameHash": "0"}