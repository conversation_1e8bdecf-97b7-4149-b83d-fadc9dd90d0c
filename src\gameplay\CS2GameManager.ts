import { EventEmitter } from 'events';
import { RoundSystem } from './RoundSystem';
import { CS2EconomySystem } from './CS2EconomySystem';
import { CS2GameModes } from './CS2GameModes';
import { CS2RankingSystem, MatchResult } from './CS2RankingSystem';
import { Vector3 } from '../shared/types';

export interface CS2Match {
    id: string;
    gameMode: string;
    map: string;
    players: string[];
    startTime: Date;
    endTime?: Date;
    status: 'waiting' | 'warmup' | 'live' | 'finished' | 'cancelled';
    score: {
        terrorist: number;
        counterTerrorist: number;
    };
    rounds: number;
    mvp?: string;
}

export interface PlayerMatchStats {
    playerId: string;
    team: 'terrorist' | 'counter_terrorist';
    kills: number;
    deaths: number;
    assists: number;
    damage: number;
    headshots: number;
    bombPlants: number;
    bombDefuses: number;
    mvpRounds: number;
    score: number;
    adr: number; // Average Damage per Round
    kast: number; // Kill, Assist, Survive, Trade percentage
    rating: number; // Overall rating
}

export interface CS2ServerConfig {
    tickRate: number;
    maxPlayers: number;
    friendlyFire: boolean;
    autoBalance: boolean;
    roundRestartDelay: number;
    maxRounds: number;
    timeLimit: number;
    antiCheat: boolean;
}

export class CS2GameManager extends EventEmitter {
    private roundSystem?: RoundSystem;
    private economySystem?: CS2EconomySystem;
    private gameModes: CS2GameModes;
    private rankingSystem: CS2RankingSystem;
    private currentMatch?: CS2Match;
    private playerStats: Map<string, PlayerMatchStats> = new Map();
    private serverConfig: CS2ServerConfig;

    // Configuração padrão do servidor
    private static readonly DEFAULT_CONFIG: CS2ServerConfig = {
        tickRate: 128,
        maxPlayers: 10,
        friendlyFire: false,
        autoBalance: true,
        roundRestartDelay: 5,
        maxRounds: 24,
        timeLimit: 0,
        antiCheat: true
    };

    constructor(config?: Partial<CS2ServerConfig>) {
        super();
        this.serverConfig = { ...CS2GameManager.DEFAULT_CONFIG, ...config };
        this.gameModes = new CS2GameModes();
        this.rankingSystem = new CS2RankingSystem();
        this.setupEventListeners();
        console.log('🎮 CS2 Game Manager inicializado');
    }

    private setupEventListeners(): void {
        // Listeners do sistema de game modes
        this.gameModes.on('matchStarted', (data) => {
            this.handleMatchStarted(data);
        });

        this.gameModes.on('roundStarted', (data) => {
            this.emit('roundStarted', data);
        });

        this.gameModes.on('roundEnded', (data) => {
            this.handleRoundEnded(data);
        });

        this.gameModes.on('matchEnded', (data) => {
            this.handleMatchEnded(data);
        });

        this.gameModes.on('playerEliminated', (data) => {
            this.handlePlayerEliminated(data);
        });

        // Listeners do sistema de ranking
        this.rankingSystem.on('rankChanged', (data) => {
            console.log(`🏆 ${data.playerId} subiu de rank: ${data.oldRank} → ${data.newRank}`);
            this.emit('playerRankChanged', data);
        });

        this.rankingSystem.on('placementCompleted', (data) => {
            console.log(`🎯 ${data.playerId} completou placement matches - Rank: ${data.finalRank}`);
            this.emit('playerPlacementCompleted', data);
        });
    }

    public async createMatch(
        gameModeId: string,
        playerIds: string[],
        mapName?: string
    ): Promise<string | null> {
        try {
            // Valida modo de jogo
            const gameMode = this.gameModes.getGameModes().find(gm => gm.id === gameModeId);
            if (!gameMode) {
                console.log(`❌ Modo de jogo inválido: ${gameModeId}`);
                return null;
            }

            // Valida número de jogadores
            if (playerIds.length > gameMode.maxPlayers) {
                console.log(`❌ Muitos jogadores: ${playerIds.length}/${gameMode.maxPlayers}`);
                return null;
            }

            // Cria match
            const matchId = `match_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            
            this.currentMatch = {
                id: matchId,
                gameMode: gameModeId,
                map: mapName || 'de_dust2',
                players: [...playerIds],
                startTime: new Date(),
                status: 'waiting',
                score: { terrorist: 0, counterTerrorist: 0 },
                rounds: 0
            };

            // Inicializa estatísticas dos jogadores
            this.initializePlayerStats(playerIds);

            // Inicia match no sistema de game modes
            const success = this.gameModes.startMatch(gameModeId, playerIds);
            
            if (success) {
                this.currentMatch.status = 'warmup';
                console.log(`🚀 Match criado: ${matchId} (${gameMode.name})`);
                this.emit('matchCreated', this.currentMatch);
                return matchId;
            } else {
                this.currentMatch = undefined;
                return null;
            }

        } catch (error) {
            console.error('❌ Erro ao criar match:', error);
            return null;
        }
    }

    private initializePlayerStats(playerIds: string[]): void {
        this.playerStats.clear();
        
        playerIds.forEach((playerId, index) => {
            const team = index % 2 === 0 ? 'terrorist' : 'counter_terrorist';
            
            this.playerStats.set(playerId, {
                playerId,
                team,
                kills: 0,
                deaths: 0,
                assists: 0,
                damage: 0,
                headshots: 0,
                bombPlants: 0,
                bombDefuses: 0,
                mvpRounds: 0,
                score: 0,
                adr: 0,
                kast: 0,
                rating: 1.0
            });
        });
    }

    private handleMatchStarted(data: any): void {
        if (!this.currentMatch) return;

        this.currentMatch.status = 'live';
        this.roundSystem = this.gameModes.getRoundSystem();
        this.economySystem = this.gameModes.getEconomySystem();

        console.log(`🎯 Match iniciado: ${this.currentMatch.id}`);
        this.emit('matchStarted', this.currentMatch);
    }

    private handleRoundEnded(data: any): void {
        if (!this.currentMatch) return;

        this.currentMatch.rounds++;
        this.currentMatch.score = {
            terrorist: data.newScore.terroristRounds,
            counterTerrorist: data.newScore.counterTerroristRounds
        };

        // Atualiza estatísticas dos jogadores
        this.updatePlayerStatsFromRound(data.playerStats);

        // Calcula MVP do round
        const mvp = this.calculateRoundMVP(data.playerStats);
        if (mvp) {
            const mvpStats = this.playerStats.get(mvp);
            if (mvpStats) {
                mvpStats.mvpRounds++;
            }
        }

        this.emit('roundEnded', {
            ...data,
            mvp,
            matchStats: Array.from(this.playerStats.values())
        });
    }

    private handleMatchEnded(data: any): void {
        if (!this.currentMatch) return;

        this.currentMatch.status = 'finished';
        this.currentMatch.endTime = new Date();
        this.currentMatch.mvp = this.calculateMatchMVP();

        // Processa resultados para o sistema de ranking
        this.processRankingResults();

        console.log(`🏆 Match finalizado: ${this.currentMatch.id} - Vencedor: ${data.winner}`);
        this.emit('matchEnded', {
            match: this.currentMatch,
            finalStats: Array.from(this.playerStats.values()),
            winner: data.winner
        });
    }

    private handlePlayerEliminated(data: any): void {
        const { playerId, killerId } = data;

        // Atualiza estatísticas
        const victimStats = this.playerStats.get(playerId);
        if (victimStats) {
            victimStats.deaths++;
        }

        if (killerId) {
            const killerStats = this.playerStats.get(killerId);
            if (killerStats) {
                killerStats.kills++;
                killerStats.score += 100; // Pontos por kill
            }

            // Adiciona reward econômico
            if (this.economySystem) {
                this.economySystem.addKillReward(killerId, 'ak47', playerId); // Weapon seria detectada dinamicamente
            }
        }

        this.emit('playerEliminated', data);
    }

    private updatePlayerStatsFromRound(roundStats: any[]): void {
        roundStats.forEach((roundStat) => {
            const playerStats = this.playerStats.get(roundStat.playerId);
            if (!playerStats) return;

            playerStats.kills += roundStat.kills;
            playerStats.deaths += roundStat.deaths;
            playerStats.assists += roundStat.assists;
            playerStats.damage += roundStat.damage;
            playerStats.bombPlants += roundStat.bombPlants;
            playerStats.bombDefuses += roundStat.bombDefuses;
        });

        // Recalcula métricas derivadas
        this.calculateDerivedStats();
    }

    private calculateDerivedStats(): void {
        this.playerStats.forEach((stats) => {
            // ADR (Average Damage per Round)
            stats.adr = this.currentMatch ? stats.damage / this.currentMatch.rounds : 0;

            // Rating simplificado (baseado em HLTV 2.0)
            const kpr = this.currentMatch ? stats.kills / this.currentMatch.rounds : 0;
            const spr = this.currentMatch ? Math.max(0, this.currentMatch.rounds - stats.deaths) / this.currentMatch.rounds : 0;
            const rmk = stats.deaths > 0 ? stats.kills / stats.deaths : stats.kills;

            stats.rating = (kpr + 0.3 * spr + rmk) / 2.7;

            // KAST% (simplificado - em um jogo real seria mais complexo)
            stats.kast = this.currentMatch ? 
                Math.min(100, ((stats.kills + stats.assists) / this.currentMatch.rounds) * 100) : 0;
        });
    }

    private calculateRoundMVP(roundStats: any[]): string | undefined {
        if (roundStats.length === 0) return undefined;

        // MVP baseado em kills, damage e objetivos
        let bestPlayer = roundStats[0];
        let bestScore = 0;

        roundStats.forEach((stats) => {
            let score = stats.kills * 100 + stats.damage * 0.5 + stats.assists * 50;
            score += stats.bombPlants * 200 + stats.bombDefuses * 250;

            if (score > bestScore) {
                bestScore = score;
                bestPlayer = stats;
            }
        });

        return bestPlayer.playerId;
    }

    private calculateMatchMVP(): string | undefined {
        if (this.playerStats.size === 0) return undefined;

        let bestPlayer: string | undefined;
        let bestRating = 0;

        this.playerStats.forEach((stats, playerId) => {
            if (stats.rating > bestRating) {
                bestRating = stats.rating;
                bestPlayer = playerId;
            }
        });

        return bestPlayer;
    }

    private processRankingResults(): void {
        if (!this.currentMatch) return;

        const winningTeam = this.currentMatch.score.terrorist > this.currentMatch.score.counterTerrorist 
            ? 'terrorist' : 'counter_terrorist';

        this.playerStats.forEach((stats) => {
            const won = stats.team === winningTeam;
            const tied = this.currentMatch!.score.terrorist === this.currentMatch!.score.counterTerrorist;

            // Calcula ELO médio dos inimigos
            const enemyStats = Array.from(this.playerStats.values())
                .filter(s => s.team !== stats.team);
            const averageEnemyElo = enemyStats.length > 0 ? 
                enemyStats.reduce((sum, s) => sum + 1500, 0) / enemyStats.length : 1500; // Placeholder ELO

            const matchResult: MatchResult = {
                playerId: stats.playerId,
                won,
                tied,
                roundsWon: stats.team === 'terrorist' ? this.currentMatch!.score.terrorist : this.currentMatch!.score.counterTerrorist,
                roundsLost: stats.team === 'terrorist' ? this.currentMatch!.score.counterTerrorist : this.currentMatch!.score.terrorist,
                kills: stats.kills,
                deaths: stats.deaths,
                assists: stats.assists,
                mvpRounds: stats.mvpRounds,
                score: stats.score,
                averageEnemyElo
            };

            this.rankingSystem.processMatchResult(matchResult);
        });
    }

    public buyItem(playerId: string, itemId: string): boolean {
        if (!this.economySystem) {
            console.log('❌ Sistema de economia não inicializado');
            return false;
        }

        return this.economySystem.buyItem(playerId, itemId);
    }

    public plantBomb(playerId: string, site: 'A' | 'B', position: Vector3): boolean {
        if (!this.roundSystem) {
            console.log('❌ Sistema de rounds não inicializado');
            return false;
        }

        return this.roundSystem.plantBomb(playerId, site, position);
    }

    public defuseBomb(playerId: string, hasDefuseKit: boolean = false): boolean {
        if (!this.roundSystem) {
            console.log('❌ Sistema de rounds não inicializado');
            return false;
        }

        return this.roundSystem.defuseBomb(playerId, hasDefuseKit);
    }

    public eliminatePlayer(playerId: string, killerId?: string): void {
        this.gameModes.eliminatePlayer(playerId, killerId);
    }

    public addDamage(playerId: string, damage: number): void {
        const stats = this.playerStats.get(playerId);
        if (stats) {
            stats.damage += damage;
        }
    }

    public addHeadshot(playerId: string): void {
        const stats = this.playerStats.get(playerId);
        if (stats) {
            stats.headshots++;
        }
    }

    // Getters públicos
    public getCurrentMatch(): CS2Match | undefined {
        return this.currentMatch;
    }

    public getPlayerStats(playerId: string): PlayerMatchStats | undefined {
        return this.playerStats.get(playerId);
    }

    public getAllPlayerStats(): PlayerMatchStats[] {
        return Array.from(this.playerStats.values());
    }

    public getRankingSystem(): CS2RankingSystem {
        return this.rankingSystem;
    }

    public getGameModes(): CS2GameModes {
        return this.gameModes;
    }

    public getServerConfig(): CS2ServerConfig {
        return { ...this.serverConfig };
    }

    public updateServerConfig(config: Partial<CS2ServerConfig>): void {
        this.serverConfig = { ...this.serverConfig, ...config };
        console.log('⚙️ Configuração do servidor atualizada');
        this.emit('serverConfigUpdated', this.serverConfig);
    }
}
