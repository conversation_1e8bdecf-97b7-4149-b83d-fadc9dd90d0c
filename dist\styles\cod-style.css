/* Call of Duty / Warzone Style Theme */

/* Global COD-style variables */
:root {
    --cod-primary-green: #64ff64;
    --cod-secondary-green: #4a9f4a;
    --cod-dark-bg: #0a0a0a;
    --cod-medium-bg: #1a1a1a;
    --cod-light-bg: #2a2a2a;
    --cod-accent-yellow: #ffff64;
    --cod-text-primary: #ffffff;
    --cod-text-secondary: #cccccc;
    --cod-text-muted: #888888;
    --cod-border: rgba(100, 150, 100, 0.2);
    --cod-hover: rgba(100, 150, 100, 0.1);
}

/* COD-style cards and panels */
.cod-panel {
    background: linear-gradient(135deg, rgba(20, 25, 30, 0.95), rgba(10, 15, 20, 0.95));
    border: 1px solid var(--cod-border);
    border-radius: 2px;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.6);
}

.cod-card {
    background: linear-gradient(135deg, rgba(30, 35, 40, 0.9), rgba(20, 25, 30, 0.9));
    border: 1px solid var(--cod-border);
    border-radius: 2px;
    padding: 20px;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.cod-card:hover {
    border-color: var(--cod-primary-green);
    box-shadow: 0 0 15px rgba(100, 255, 100, 0.2);
    transform: translateY(-2px);
}

.cod-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--cod-primary-green), transparent);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.cod-card:hover::before {
    opacity: 1;
}

/* COD-style buttons */
.cod-btn {
    background: linear-gradient(135deg, rgba(30, 35, 40, 0.9), rgba(20, 25, 30, 0.9));
    border: 1px solid var(--cod-border);
    color: var(--cod-text-primary);
    padding: 10px 20px;
    border-radius: 2px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.cod-btn:hover {
    border-color: var(--cod-primary-green);
    color: var(--cod-primary-green);
    box-shadow: 0 0 10px rgba(100, 255, 100, 0.2);
}

.cod-btn.primary {
    background: linear-gradient(135deg, var(--cod-secondary-green), var(--cod-primary-green));
    border-color: var(--cod-primary-green);
    color: #000000;
}

.cod-btn.primary:hover {
    background: var(--cod-primary-green);
    box-shadow: 0 0 15px rgba(100, 255, 100, 0.4);
}

/* COD-style weapon cards */
.weapon-card-cod {
    background: linear-gradient(135deg, rgba(25, 30, 35, 0.95), rgba(15, 20, 25, 0.95));
    border: 1px solid rgba(100, 150, 100, 0.15);
    border-radius: 2px;
    padding: 15px;
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.weapon-card-cod::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(100, 255, 100, 0.1), transparent);
    transition: left 0.3s ease;
}

.weapon-card-cod:hover::before {
    left: 100%;
}

.weapon-card-cod:hover {
    border-color: var(--cod-primary-green);
    box-shadow: 0 0 20px rgba(100, 255, 100, 0.2);
    transform: translateY(-3px);
}

.weapon-card-cod.active {
    border-color: var(--cod-primary-green);
    background: linear-gradient(135deg, rgba(100, 150, 100, 0.2), rgba(50, 75, 50, 0.2));
    box-shadow: 0 0 25px rgba(100, 255, 100, 0.3);
}

.weapon-card-cod .weapon-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 2px;
    margin-bottom: 10px;
    filter: brightness(0.8) contrast(1.1);
    transition: filter 0.2s ease;
}

.weapon-card-cod:hover .weapon-image {
    filter: brightness(1) contrast(1.2);
}

.weapon-card-cod h4 {
    color: var(--cod-text-primary);
    font-size: 0.9rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
}

/* COD-style stats bars */
.stat-bar-cod {
    background: rgba(0, 0, 0, 0.5);
    height: 4px;
    border-radius: 2px;
    overflow: hidden;
    margin: 4px 0;
    position: relative;
}

.stat-bar-cod .fill {
    height: 100%;
    background: linear-gradient(90deg, var(--cod-secondary-green), var(--cod-primary-green));
    border-radius: 2px;
    transition: width 0.3s ease;
    position: relative;
}

.stat-bar-cod .fill::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 2px;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    box-shadow: 0 0 4px rgba(255, 255, 255, 0.5);
}

/* COD-style navigation tabs */
.cod-tabs {
    display: flex;
    background: rgba(0, 0, 0, 0.8);
    border-bottom: 1px solid var(--cod-border);
}

.cod-tab {
    padding: 15px 25px;
    background: rgba(20, 25, 30, 0.8);
    border-right: 1px solid var(--cod-border);
    color: var(--cod-text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.85rem;
}

.cod-tab:hover {
    background: var(--cod-hover);
    color: var(--cod-text-primary);
}

.cod-tab.active {
    background: linear-gradient(135deg, rgba(100, 150, 100, 0.2), rgba(50, 75, 50, 0.2));
    color: var(--cod-primary-green);
    border-bottom: 2px solid var(--cod-primary-green);
}

/* COD-style input fields */
.cod-input {
    background: rgba(0, 0, 0, 0.6);
    border: 1px solid var(--cod-border);
    border-radius: 2px;
    padding: 10px 15px;
    color: var(--cod-text-primary);
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.cod-input:focus {
    outline: none;
    border-color: var(--cod-primary-green);
    box-shadow: 0 0 10px rgba(100, 255, 100, 0.2);
}

/* COD-style progress bars */
.cod-progress {
    background: rgba(0, 0, 0, 0.6);
    height: 6px;
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.cod-progress .bar {
    height: 100%;
    background: linear-gradient(90deg, var(--cod-secondary-green), var(--cod-primary-green));
    border-radius: 3px;
    transition: width 0.3s ease;
    position: relative;
}

.cod-progress .bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* COD-style tooltips */
.cod-tooltip {
    position: relative;
}

.cod-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: var(--cod-text-primary);
    padding: 8px 12px;
    border-radius: 2px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
    border: 1px solid var(--cod-border);
    z-index: 1000;
}

.cod-tooltip:hover::after {
    opacity: 1;
}

/* COD-style scrollbars */
.cod-scroll::-webkit-scrollbar {
    width: 8px;
}

.cod-scroll::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3);
}

.cod-scroll::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, var(--cod-secondary-green), var(--cod-primary-green));
    border-radius: 4px;
}

.cod-scroll::-webkit-scrollbar-thumb:hover {
    background: var(--cod-primary-green);
}
